const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  // Ensure we're on Polygon mainnet
  if (hre.network.name !== "polygon") {
    console.error("❌ This script is specifically for Polygon mainnet deployment.");
    console.error(`Current network: ${hre.network.name}`);
    console.error("Please run: npx hardhat run scripts/deploy-polygon-mainnet.js --network polygon");
    process.exit(1);
  }

  console.log("🚀 Starting Polygon Mainnet Deployment...");
  console.log("🌐 Network:", hre.network.name);
  console.log("⛽ Chain ID:", hre.network.config.chainId);

  // Validate environment variables
  if (!process.env.PRIVATE_KEY) {
    console.error("❌ PRIVATE_KEY not found in .env file");
    process.exit(1);
  }

  if (!process.env.POLYGONSCAN_API_KEY) {
    console.error("❌ POLYGONSCAN_API_KEY not found in .env file");
    console.error("This is required for contract verification");
    process.exit(1);
  }

  const [deployer] = await hre.ethers.getSigners();
  console.log("👤 Deploying from address:", deployer.address);
  
  // Check balance
  const balance = await hre.ethers.provider.getBalance(deployer.address);
  const balanceInMatic = hre.ethers.formatEther(balance);
  console.log("💰 Deployer balance:", balanceInMatic, "MATIC");
  
  // Ensure we have enough balance for deployment (minimum 0.5 MATIC recommended)
  if (balance < hre.ethers.parseEther("0.5")) {
    console.error("❌ Insufficient balance for mainnet deployment.");
    console.error("Recommended minimum: 0.5 MATIC for gas fees");
    console.error(`Current balance: ${balanceInMatic} MATIC`);
    process.exit(1);
  }

  // Confirm deployment
  console.log("\n⚠️  MAINNET DEPLOYMENT CONFIRMATION ⚠️");
  console.log("You are about to deploy to Polygon Mainnet");
  console.log("This will cost real MATIC tokens");
  console.log("Make sure you have reviewed your contracts thoroughly");
  
  // In a real scenario, you might want to add a confirmation prompt here
  // For automation, we'll proceed directly
  
  console.log("\n🔄 Proceeding with deployment...");

  try {
    // 1️⃣ Deploy BDIOCoreRegistry
    console.log("\n📋 Deploying BDIOCoreRegistry...");
    const BDIOCoreRegistry = await hre.ethers.getContractFactory("BDIOCoreRegistry");
    const bdioCoreRegistry = await BDIOCoreRegistry.deploy();
    await bdioCoreRegistry.waitForDeployment();
    const bdioCoreRegistryAddress = await bdioCoreRegistry.getAddress();
    console.log(`✅ BDIOCoreRegistry deployed at: ${bdioCoreRegistryAddress}`);
    
    // Wait for confirmations
    console.log("⏳ Waiting for 5 block confirmations...");
    await bdioCoreRegistry.deploymentTransaction().wait(5);

    // 2️⃣ Deploy AccessControlManager with dummy endorsementManager
    console.log("\n🔐 Deploying AccessControlManager...");
    const dummyEndorsementManager = "******************************************";
    const AccessControlManager = await hre.ethers.getContractFactory("AccessControlManager");
    const accessControlManager = await AccessControlManager.deploy(
      bdioCoreRegistryAddress,
      dummyEndorsementManager
    );
    await accessControlManager.waitForDeployment();
    const accessControlManagerAddress = await accessControlManager.getAddress();
    console.log(`✅ AccessControlManager deployed at: ${accessControlManagerAddress}`);
    
    console.log("⏳ Waiting for 5 block confirmations...");
    await accessControlManager.deploymentTransaction().wait(5);

    // 3️⃣ Deploy EndorsementManager
    console.log("\n📝 Deploying EndorsementManager...");
    const EndorsementManager = await hre.ethers.getContractFactory("EndorsementManager");
    const endorsementManager = await EndorsementManager.deploy(
      accessControlManagerAddress,
      bdioCoreRegistryAddress
    );
    await endorsementManager.waitForDeployment();
    const endorsementManagerAddress = await endorsementManager.getAddress();
    console.log(`✅ EndorsementManager deployed at: ${endorsementManagerAddress}`);
    
    console.log("⏳ Waiting for 5 block confirmations...");
    await endorsementManager.deploymentTransaction().wait(5);

    // 4️⃣ Update AccessControlManager
    console.log("\n🔄 Updating AccessControlManager with real EndorsementManager address...");
    const accessControlManagerContract = AccessControlManager.attach(accessControlManagerAddress);
    const updateTx = await accessControlManagerContract.setEndorsementManager(endorsementManagerAddress);
    await updateTx.wait();
    console.log(`✅ AccessControlManager updated successfully`);

    // 5️⃣ Deploy BDIOExpiryManager
    console.log("\n⏰ Deploying BDIOExpiryManager...");
    const BDIOExpiryManager = await hre.ethers.getContractFactory("BDIOExpiryManager");
    const bdioExpiryManager = await BDIOExpiryManager.deploy(bdioCoreRegistryAddress);
    await bdioExpiryManager.waitForDeployment();
    const bdioExpiryManagerAddress = await bdioExpiryManager.getAddress();
    console.log(`✅ BDIOExpiryManager deployed at: ${bdioExpiryManagerAddress}`);
    
    console.log("⏳ Waiting for 5 block confirmations...");
    await bdioExpiryManager.deploymentTransaction().wait(5);

    // 6️⃣ Deploy VCManager
    console.log("\n📄 Deploying VCManager...");
    const VCManager = await hre.ethers.getContractFactory("VCManager");
    const vcManager = await VCManager.deploy(bdioCoreRegistryAddress);
    await vcManager.waitForDeployment();
    const vcManagerAddress = await vcManager.getAddress();
    console.log(`✅ VCManager deployed at: ${vcManagerAddress}`);
    
    console.log("⏳ Waiting for 5 block confirmations...");
    await vcManager.deploymentTransaction().wait(5);

    // Save deployment information
    console.log("\n💾 Saving deployment information...");
    const deployments = {
      BDIOCoreRegistry: bdioCoreRegistryAddress,
      AccessControlManager: accessControlManagerAddress,
      EndorsementManager: endorsementManagerAddress,
      BDIOExpiryManager: bdioExpiryManagerAddress,
      VCManager: vcManagerAddress,
      network: hre.network.name,
      chainId: hre.network.config.chainId,
      deployer: deployer.address,
      deployedAt: new Date().toISOString(),
      polygonScanUrls: {
        BDIOCoreRegistry: `https://polygonscan.com/address/${bdioCoreRegistryAddress}`,
        AccessControlManager: `https://polygonscan.com/address/${accessControlManagerAddress}`,
        EndorsementManager: `https://polygonscan.com/address/${endorsementManagerAddress}`,
        BDIOExpiryManager: `https://polygonscan.com/address/${bdioExpiryManagerAddress}`,
        VCManager: `https://polygonscan.com/address/${vcManagerAddress}`
      }
    };

    const filePath = path.join(__dirname, "..", "deployments.json");
    fs.writeFileSync(filePath, JSON.stringify(deployments, null, 2));

    // Also save a mainnet-specific backup
    const mainnetFilePath = path.join(__dirname, "..", `deployments-polygon-mainnet-${Date.now()}.json`);
    fs.writeFileSync(mainnetFilePath, JSON.stringify(deployments, null, 2));

    console.log("\n🎉 POLYGON MAINNET DEPLOYMENT SUCCESSFUL! 🎉");
    console.log("\n📋 Contract Addresses:");
    console.log(`BDIOCoreRegistry: ${bdioCoreRegistryAddress}`);
    console.log(`AccessControlManager: ${accessControlManagerAddress}`);
    console.log(`EndorsementManager: ${endorsementManagerAddress}`);
    console.log(`BDIOExpiryManager: ${bdioExpiryManagerAddress}`);
    console.log(`VCManager: ${vcManagerAddress}`);

    console.log("\n🔗 PolygonScan Links:");
    Object.entries(deployments.polygonScanUrls).forEach(([name, url]) => {
      console.log(`${name}: ${url}`);
    });

    console.log("\n📋 Next Steps:");
    console.log("1. Verify contracts: npx hardhat run scripts/verify.js --network polygon");
    console.log("2. Update your .env file with new addresses");
    console.log("3. Test contract interactions");
    console.log("4. Update frontend/backend with new addresses");

  } catch (error) {
    console.error("\n❌ DEPLOYMENT FAILED:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
