{"abi": [{"inputs": [{"internalType": "address", "name": "_accessControlManager", "type": "address"}, {"internalType": "address", "name": "registryAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "note", "type": "string"}], "name": "EndorsedWithNote", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "index", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "revokedBy", "type": "address"}], "name": "EndorsementRevoked", "type": "event"}, {"inputs": [], "name": "accessControlManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address[]", "name": "signers", "type": "address[]"}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}, {"internalType": "string", "name": "note", "type": "string"}], "name": "batchEndorse", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "bdioRegistry", "outputs": [{"internalType": "contract IBDIORegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "endorsements", "outputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getAllEndorsements", "outputs": [{"components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "internalType": "struct EndorsementManager.Endorsement[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getEndorsementByIndex", "outputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getEndorsementsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "revokeEndorsement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "string", "name": "note", "type": "string"}], "name": "signWithNote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}