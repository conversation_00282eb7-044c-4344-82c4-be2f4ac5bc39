# Polygon Mainnet Deployment Guide

## Overview
This guide explains how to deploy and verify smart contracts on Polygon mainnet using the provided scripts.

## Prerequisites

### 1. Environment Setup
Create a `.env` file in the project root with the following variables:

```env
# Required for deployment
PRIVATE_KEY=0x...your_private_key_here
POLYG<PERSON>SCAN_API_KEY=your_polygonscan_api_key_here

# Optional - Custom RPC URL
RPC_URL=https://polygon-mainnet.infura.io/v3/your_project_id

# Contract addresses (will be populated after deployment)
BDIO_CORE_ADDRESS=
ACCESS_CONTROL_ADDRESS=
ENDORSEMENT_MANAGER_ADDRESS=
EXPIRY_MANAGER_ADDRESS=
VC_MANAGER_ADDRESS=
```

### 2. Required Balance
Ensure your deployer wallet has at least **0.5 MATIC** for gas fees on Polygon mainnet.

### 3. API Keys
- Get a free PolygonScan API key from: https://polygonscan.com/apis
- (Optional) Get an Infura or Alchemy API key for custom RPC

## Deployment Scripts

### 1. Deploy to Polygon Mainnet
```bash
npx hardhat run scripts/deploy-polygon-mainnet.js --network polygon
```

**Features:**
- ✅ Validates environment variables
- ✅ Checks wallet balance
- ✅ Waits for block confirmations
- ✅ Saves deployment info to `deployments.json`
- ✅ Creates timestamped backup file

### 2. Deploy to Testnet (Amoy)
```bash
npx hardhat run scripts/deploy.js --network amoy
```

### 3. General Deployment Script
```bash
npx hardhat run scripts/deploy.js --network <network_name>
```

## Verification Scripts

### 1. Verify on Polygon Mainnet
```bash
npx hardhat run scripts/verify-polygon-mainnet.js --network polygon
```

**Features:**
- ✅ Comprehensive error handling
- ✅ Rate limiting protection
- ✅ Detailed verification summary
- ✅ Direct PolygonScan links

### 2. General Verification
```bash
npx hardhat run scripts/verify.js --network polygon
```

## Utility Scripts

### 1. Check Deployment Status
```bash
npx hardhat run scripts/check-deployment-status.js --network polygon
```

**Shows:**
- 📋 Contract addresses
- 🔗 Explorer links
- 🔍 Contract existence verification
- 🔧 Environment variables status
- 📝 Available commands

## Contract Architecture

The deployment follows this sequence:

1. **BDIOCoreRegistry** - Core registry contract
2. **AccessControlManager** - Access control with dummy endorsement manager
3. **EndorsementManager** - Real endorsement manager
4. **Update AccessControlManager** - Set real endorsement manager address
5. **BDIOExpiryManager** - Expiry management
6. **VCManager** - Verifiable credentials management

## Network Configuration

### Polygon Mainnet
- **Chain ID:** 137
- **RPC:** https://polygon-rpc.com (or custom RPC_URL)
- **Explorer:** https://polygonscan.com
- **Gas Price:** 50 gwei (configurable)
- **Gas Limit:** 6M

### Polygon Amoy Testnet
- **Chain ID:** 80002
- **RPC:** https://rpc-amoy.polygon.technology
- **Explorer:** https://amoy.polygonscan.com
- **Gas Price:** 30 gwei

## File Structure

```
scripts/
├── deploy.js                      # General deployment script
├── deploy-polygon-mainnet.js      # Mainnet-specific deployment
├── verify.js                      # General verification script
├── verify-polygon-mainnet.js      # Mainnet-specific verification
└── check-deployment-status.js     # Status checker

deployments.json                   # Latest deployment info
deployments-polygon-mainnet-*.json # Mainnet deployment backups
```

## Troubleshooting

### Common Issues

1. **Insufficient Balance**
   ```
   Error: Insufficient balance for mainnet deployment
   ```
   **Solution:** Add more MATIC to your wallet

2. **Invalid API Key**
   ```
   Error: Invalid PolygonScan API key
   ```
   **Solution:** Check your `POLYGONSCAN_API_KEY` in `.env`

3. **Rate Limited**
   ```
   Error: Rate limited by PolygonScan
   ```
   **Solution:** Wait a few minutes and retry verification

4. **Contract Not Found**
   ```
   Error: Contract not found at address
   ```
   **Solution:** Ensure deployment was successful and you're on the correct network

### Gas Issues

If transactions fail due to gas:

1. **Check current gas prices:** https://polygonscan.com/gastracker
2. **Update hardhat.config.js:**
   ```javascript
   polygon: {
     gasPrice: 80000000000, // 80 gwei - adjust as needed
   }
   ```

### Network Issues

If RPC issues occur:
1. Use a custom RPC URL in `.env`
2. Try alternative RPCs:
   - `https://polygon-mainnet.infura.io/v3/YOUR_KEY`
   - `https://polygon-mainnet.g.alchemy.com/v2/YOUR_KEY`
   - `https://rpc.ankr.com/polygon`

## Security Checklist

Before mainnet deployment:

- [ ] ✅ Contracts audited and tested
- [ ] ✅ Private key is secure and not shared
- [ ] ✅ Sufficient MATIC balance
- [ ] ✅ Environment variables properly set
- [ ] ✅ Testnet deployment successful
- [ ] ✅ Contract verification working

## Post-Deployment

After successful deployment:

1. **Update Environment Variables**
   ```env
   BDIO_CORE_ADDRESS=0x...
   ACCESS_CONTROL_ADDRESS=0x...
   ENDORSEMENT_MANAGER_ADDRESS=0x...
   EXPIRY_MANAGER_ADDRESS=0x...
   VC_MANAGER_ADDRESS=0x...
   ```

2. **Verify All Contracts**
   ```bash
   npx hardhat run scripts/verify-polygon-mainnet.js --network polygon
   ```

3. **Test Contract Interactions**
   - Use the contract addresses in your frontend/backend
   - Test basic functionality
   - Monitor for any issues

4. **Backup Important Files**
   - Save `deployments.json`
   - Backup your `.env` file securely
   - Keep deployment transaction hashes

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Hardhat documentation: https://hardhat.org/docs
3. Check Polygon documentation: https://docs.polygon.technology/
