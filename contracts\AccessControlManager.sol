// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

interface IEndorsementManager {
    function getEndorsementsCount(string calldata bdioId) external view returns (uint);
    function getEndorsementByIndex(string calldata bdioId, uint index) external view returns (
        address signer,
        bytes memory signature,
        uint timestamp,
        string memory note
    );
}

contract AccessControlManager is ReentrancyGuard, Pausable {
    IBDIORegistry public bdioRegistry;
    address public endorsementManager;
    address public contractOwner;
    address public pendingOwner;

    // Limit signer per document (default: 10)
    uint256 public maxSignersPerDocument = 10;
    uint256 public constant MAX_PAGINATION_LIMIT = 100;

    // mapping: bdioId => signer => approved status
    mapping(string => mapping(address => bool)) public approvedSigners;

    // List all signer addresses ever approved (used to track revoked)
    mapping(string => address[]) private signerList;

    // Events
    event SignerApproved(string indexed bdioId, address indexed signer);
    event SignerRevoked(string indexed bdioId, address indexed signer);
    event EndorsementManagerUpdated(address indexed oldAddress, address indexed newAddress);
    event MaxSignersPerDocumentUpdated(uint256 oldMax, uint256 newMax);
    event RegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event OwnershipTransferInitiated(address indexed currentOwner, address indexed newOwner);
    event OwnershipTransferCompleted(address indexed oldOwner, address indexed newOwner);

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocumentOwner(string calldata bdioId) {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can perform this action");
        _;
    }

    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }

    modifier validContract(address addr) {
        require(addr != address(0), "Invalid address");
        require(addr.code.length > 0, "Not a contract");
        _;
    }

    modifier validBdioId(string calldata bdioId) {
        require(bytes(bdioId).length >= 10 && bytes(bdioId).length <= 64, "Invalid BDIO ID");
        _;
    }

    constructor(address registryAddress, address _endorsementManager) {
        require(registryAddress != address(0), "Invalid registry address");
        require(_endorsementManager != address(0), "Invalid endorsement manager");
        
        bdioRegistry = IBDIORegistry(registryAddress);
        endorsementManager = _endorsementManager;
        contractOwner = msg.sender;
    }

    /// Approve a signer for a document
    function approveSigner(string calldata bdioId, address signer) external 
        onlyDocumentOwner(bdioId)
        validAddress(signer)
        validBdioId(bdioId)
        whenNotPaused
        nonReentrant
    {
        require(!approvedSigners[bdioId][signer], "Signer already approved");
        require(signerList[bdioId].length < maxSignersPerDocument, "Max signers reached");

        approvedSigners[bdioId][signer] = true;
        signerList[bdioId].push(signer);
        
        emit SignerApproved(bdioId, signer);
    }

    /// Batch approve multiple signers
    function batchApproveSigner(string calldata bdioId, address[] calldata signers) external 
        onlyDocumentOwner(bdioId)
        validBdioId(bdioId)
        whenNotPaused
        nonReentrant
    {
        require(signers.length > 0 && signers.length <= 20, "Invalid batch size");
        require(signerList[bdioId].length + signers.length <= maxSignersPerDocument, "Would exceed max signers");

        for (uint256 i = 0; i < signers.length; i++) {
            require(signers[i] != address(0), "Invalid signer address");
            require(!approvedSigners[bdioId][signers[i]], "Signer already approved");
            
            approvedSigners[bdioId][signers[i]] = true;
            signerList[bdioId].push(signers[i]);
            
            emit SignerApproved(bdioId, signers[i]);
        }
    }

    /// Revoke a previously approved signer
    function revokeSigner(string calldata bdioId, address signer) external 
        onlyDocumentOwner(bdioId)
        validAddress(signer)
        validBdioId(bdioId)
        whenNotPaused
        nonReentrant
    {
        require(approvedSigners[bdioId][signer], "Signer not approved");

        approvedSigners[bdioId][signer] = false;
        emit SignerRevoked(bdioId, signer);
    }

    /// Check if signer is currently approved
    function isSignerApproved(string calldata bdioId, address signer) external view returns (bool) {
        return approvedSigners[bdioId][signer];
    }

    /// Get all signers ever approved (including revoked) with pagination
    function getApprovedSignersPaginated(string calldata bdioId, uint256 offset, uint256 limit) 
        external view returns (address[] memory signers, uint256 total) 
    {
        require(limit > 0 && limit <= MAX_PAGINATION_LIMIT, "Invalid limit");
        
        address[] memory allSigners = signerList[bdioId];
        total = allSigners.length;
        
        if (offset >= total) {
            return (new address[](0), total);
        }
        
        uint256 end = offset + limit;
        if (end > total) {
            end = total;
        }
        
        signers = new address[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            signers[i - offset] = allSigners[i];
        }
    }

    /// Get all signers ever approved (backward compatibility)
    function getApprovedSigners(string calldata bdioId) external view returns (address[] memory) {
        return signerList[bdioId];
    }

    /// Get signer status with endorsement check (with pagination to prevent DoS)
    function getSignerStatusPaginated(
        string calldata bdioId, 
        address signer,
        uint256 startIndex,
        uint256 limit
    ) external view returns (
        bool approved, 
        bool signed, 
        bool revoked, 
        uint256 nextIndex,
        uint256 totalEndorsements
    ) {
        require(limit > 0 && limit <= MAX_PAGINATION_LIMIT, "Invalid limit");
        
        approved = approvedSigners[bdioId][signer];
        
        // Check if signer was ever approved but now revoked
        bool wasEverApproved = false;
        address[] memory allSigners = signerList[bdioId];
        for (uint256 i = 0; i < allSigners.length; i++) {
            if (allSigners[i] == signer) {
                wasEverApproved = true;
                break;
            }
        }
        revoked = wasEverApproved && !approved;

        // Check endorsements with pagination
        if (endorsementManager != address(0)) {
            try IEndorsementManager(endorsementManager).getEndorsementsCount(bdioId) returns (uint256 count) {
                totalEndorsements = count;
                
                uint256 end = startIndex + limit;
                if (end > count) {
                    end = count;
                    nextIndex = 0; // No more items
                } else {
                    nextIndex = end;
                }
                
                for (uint256 i = startIndex; i < end; i++) {
                    try IEndorsementManager(endorsementManager).getEndorsementByIndex(bdioId, i) 
                        returns (address endorser, bytes memory, uint256, string memory) {
                        if (endorser == signer) {
                            signed = true;
                            break;
                        }
                    } catch {
                        // Continue if individual endorsement check fails
                        continue;
                    }
                }
            } catch {
                // If endorsement manager call fails, continue without endorsement data
                totalEndorsements = 0;
                nextIndex = 0;
            }
        }
    }

    /// Update endorsement manager address
    function updateEndorsementManager(address newEndorsementManager) external
        onlyContractOwner
        validContract(newEndorsementManager)
    {
        address oldAddress = endorsementManager;
        endorsementManager = newEndorsementManager;
        emit EndorsementManagerUpdated(oldAddress, newEndorsementManager);
    }

    /// Update registry address
    function updateRegistry(address newRegistry) external
        onlyContractOwner
        validContract(newRegistry)
    {
        address oldRegistry = address(bdioRegistry);
        bdioRegistry = IBDIORegistry(newRegistry);
        emit RegistryUpdated(oldRegistry, newRegistry);
    }

    /// Update max signer limit (only owner)
    function setMaxSignersPerDocument(uint256 newMax) external onlyContractOwner {
        require(newMax > 0 && newMax <= 100, "Invalid max");
        emit MaxSignersPerDocumentUpdated(maxSignersPerDocument, newMax);
        maxSignersPerDocument = newMax;
    }

    /// Emergency pause
    function pause() external onlyContractOwner {
        _pause();
    }

    function unpause() external onlyContractOwner {
        _unpause();
    }

    /// Get current signer count for a document
    function getSignerCount(string calldata bdioId) external view returns (uint256) {
        return signerList[bdioId].length;
    }

    /// Check if document has reached max signers
    function hasReachedMaxSigners(string calldata bdioId) external view returns (bool) {
        return signerList[bdioId].length >= maxSignersPerDocument;
    }

    /// @notice Initiate ownership transfer (2-step process)
    function transferOwnership(address newOwner) external onlyContractOwner validAddress(newOwner) {
        require(newOwner != contractOwner, "Same owner");
        pendingOwner = newOwner;
        emit OwnershipTransferInitiated(contractOwner, newOwner);
    }

    /// @notice Accept ownership transfer
    function acceptOwnership() external {
        require(msg.sender == pendingOwner, "Not pending owner");
        address oldOwner = contractOwner;
        contractOwner = pendingOwner;
        pendingOwner = address(0);
        emit OwnershipTransferCompleted(oldOwner, contractOwner);
    }

    /// @notice Cancel pending ownership transfer
    function cancelOwnershipTransfer() external onlyContractOwner {
        require(pendingOwner != address(0), "No pending transfer");
        pendingOwner = address(0);
    }
}