const { ethers } = require("ethers");
const { PDFDocument, StandardFonts } = require('pdf-lib');
const QRCode = require('qrcode');
const fs = require('fs');
const deployments = require("../frontend/src/deployments.json");

async function main() {
  // Setup provider
  const provider = new ethers.JsonRpcProvider("https://polygon-rpc.com"); // bisa juga pakai Alchemy / Infura

  // Contract addresses
  const coreAddress = deployments.BDIOCoreRegistry;
  const endorseAddress = deployments.EndorsementManager;

  // Load ABI (pastikan ada di folder abis/)
  const coreAbi = require("../abis/BDIOCoreRegistry.json");
  const endorseAbi = require("../abis/EndorsementManager.json");

  // Connect contract
  const core = new ethers.Contract(coreAddress, coreAbi, provider);
  const endorse = new ethers.Contract(endorseAddress, endorseAbi, provider);

  // Fetch data from blockchain (misalnya untuk bdioId = "doc123")
  const bdioId = "doc123";

  const [owner, active, archived, timestamp, versionCount, metadataUri] =
    await core.verifyDocument(bdioId);

  // Dummy signer data; real case: panggil endorse.getSigners(bdioId) atau fungsi lain
  const signers = [
    { signer: "0x4b57...", note: "Owner signed", timestamp: "2025-07-04 15:10" },
    { signer: "0x5a0b...", note: "CFO approved", timestamp: "2025-07-04 15:15" }
  ];

  console.log("Owner:", owner);
  console.log("Active:", active);
  console.log("Versions:", versionCount.toString());

  // PDF part (singkat, sama seperti sebelumnya)
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([600, 800]);
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  let y = 780;

  page.drawText(`🛡️ BDIO Audit Trail Report`, { x: 50, y, size: 16, font });
  y -= 30;

  page.drawText(`Document ID: ${bdioId}`, { x: 50, y, size: 11, font });
  y -= 15;
  page.drawText(`Owner: ${owner}`, { x: 50, y, size: 11, font });
  y -= 15;
  page.drawText(`Active: ${active}`, { x: 50, y, size: 11, font });
  y -= 15;
  page.drawText(`Version count: ${versionCount}`, { x: 50, y, size: 11, font });
  y -= 15;
  page.drawText(`Metadata URI: ${metadataUri}`, { x: 50, y, size: 11, font });
  y -= 25;

  // Signers
  page.drawText('✍️ Signers:', { x: 50, y, size: 13, font });
  y -= 20;
  signers.forEach(s => {
    page.drawText(`• ${s.signer} | ${s.note} | ${s.timestamp}`, { x: 60, y, size: 10, font });
    y -= 15;
  });

  // QR code
  const qrDataUrl = await QRCode.toDataURL(metadataUri);
  const qrImage = await pdfDoc.embedPng(qrDataUrl);
  page.drawImage(qrImage, { x: 400, y: 50, width: 120, height: 120 });

  const pdfBytes = await pdfDoc.save();
  fs.writeFileSync('frontend/public/audit-trail.pdf', pdfBytes);

  console.log('✅ PDF generated at frontend/public/audit-trail.pdf');
}

main().catch(err => {
  console.error("❌ Failed:", err);
});
