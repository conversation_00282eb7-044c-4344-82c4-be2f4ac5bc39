<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Endorse Document</title>
  <style>
    body { font-family: sans-serif; margin: 20px; }
    input, textarea, button { display: block; margin: 10px 0; width: 300px; }
    #status { margin-top: 10px; color: green; }
    #error { margin-top: 10px; color: red; }
  </style>
</head>
<body>
  <h2>Endorse Document with MetaMask</h2>
  <label>BDIO ID:
    <input type="text" id="bdioId" placeholder="e.g., a3e533f6cd0c1f5f" />
  </label>
  <label>Note:
    <textarea id="note" placeholder="Optional note about this endorsement"></textarea>
  </label>
  <button onclick="endorseDocument()">Sign & Endorse</button>

  <div id="status"></div>
  <div id="error"></div>

  <script src="https://cdn.jsdelivr.net/npm/ethers@6.8.1/dist/ethers.umd.min.js"></script>
  <script>
    // Konfigurasi kontrak
    const accessControlAddress = '******************************************';  // Ganti dengan address contractmu
    const endorsementManagerAddress = '******************************************'; // Ganti dengan address contractmu

    const accessControlAbi = [
      "function isSignerApproved(string bdioId, address signer) view returns (bool)"
    ];

    const endorsementManagerAbi = [
      "function signWithNote(string bdioId, bytes signature, string note) external"
    ];

    let provider, signer;

    async function endorseDocument() {
      document.getElementById('status').innerText = '';
      document.getElementById('error').innerText = '';

      try {
        if (!window.ethereum) {
          throw new Error('MetaMask not found');
        }

        // Connect to wallet
        provider = new ethers.BrowserProvider(window.ethereum);
        signer = await provider.getSigner();
        const signerAddress = await signer.getAddress();

        const bdioId = document.getElementById('bdioId').value.trim();
        const note = document.getElementById('note').value.trim();

        if (!bdioId) {
          throw new Error('BDIO ID is required');
        }

        // Check approval status
        const accessControlContract = new ethers.Contract(accessControlAddress, accessControlAbi, provider);
        const isApproved = await accessControlContract.isSignerApproved(bdioId, signerAddress);

        if (!isApproved) {
          throw new Error('You are not an approved signer for this document');
        }

        // Buat signature (contoh: hash dari bdioId + note)
        const message = ethers.id(bdioId + note); // atau bisa disesuaikan
        const signature = await signer.signMessage(ethers.getBytes(message));

        // Kirim ke contract
        const endorsementContract = new ethers.Contract(endorsementManagerAddress, endorsementManagerAbi, signer);
        const tx = await endorsementContract.signWithNote(bdioId, signature, note);

        document.getElementById('status').innerText = 'Transaction sent. Waiting for confirmation...';

        await tx.wait();

        document.getElementById('status').innerText = 'Endorsement successful! TxHash: ' + tx.hash;
      } catch (err) {
        console.error(err);
        document.getElementById('error').innerText = 'Error: ' + (err.message || err);
      }
    }
  </script>
</body>
</html>
