const { ethers } = require("hardhat");

async function main() {
  if (process.argv.length < 4) {
    console.error("Usage: node check-approval.js <BDIO_ID> <SIGNER_ADDRESS>");
    process.exit(1);
  }

  const bdioId = process.argv[2];
  const signerAddress = process.argv[3];

  const deployments = require("../frontend/src/deployments.json");
  const accessControlAddress = deployments.AccessControlManager;
  const endorsementAddress = deployments.EndorsementManager;

  // Update this RPC URL to your running blockchain node URL
  const rpcUrl = "http://localhost:8545";
  const provider = new ethers.JsonRpcProvider(rpcUrl);
  const AccessControlManager = await ethers.getContractFactory("AccessControlManager");
  const accessControl = AccessControlManager.attach(accessControlAddress).connect(provider);

  const EndorsementManager = await ethers.getContractFactory("EndorsementManager");
  const endorsement = EndorsementManager.attach(endorsementAddress).connect(provider);

  console.log(`Checking approval status for signer ${signerAddress} and BDIO ID ${bdioId}...`);

  const isApproved = await accessControl.isSignerApproved(bdioId, signerAddress);
  console.log(`AccessControlManager.isSignerApproved: ${isApproved}`);

  // Read accessControlManager address stored in EndorsementManager
  const storedAccessControlAddress = await endorsement.accessControlManager();
  console.log(`EndorsementManager.accessControlManager: ${storedAccessControlAddress}`);

  if (storedAccessControlAddress.toLowerCase() !== accessControlAddress.toLowerCase()) {
    console.warn("Warning: AccessControlManager address in EndorsementManager does not match deployments.json");
  }

  // Optionally, check endorsements count for BDIO ID
  const count = await endorsement.getEndorsementsCount(bdioId);
  console.log(`Endorsements count for BDIO ID ${bdioId}: ${count}`);
}

main().catch((error) => {
  console.error("Error in check-approval script:", error);
  process.exit(1);
});
