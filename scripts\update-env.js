const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🔄 Updating .env file with deployment addresses...");

  const deploymentsPath = path.join(__dirname, "..", "deployments.json");
  if (!fs.existsSync(deploymentsPath)) {
    console.error("❌ deployments.json not found. Please deploy contracts first.");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync(deploymentsPath));
  
  const envPath = path.join(__dirname, "..", ".env");
  if (!fs.existsSync(envPath)) {
    console.error("❌ .env file not found. Please create one first.");
    process.exit(1);
  }

  // Read current .env content
  let envContent = fs.readFileSync(envPath, "utf8");
  
  // Create backup
  const backupPath = path.join(__dirname, "..", `.env.backup.${Date.now()}`);
  fs.writeFileSync(backupPath, envContent);
  console.log(`📋 Backup created: ${path.basename(backupPath)}`);

  // Update or add contract addresses
  const contractMappings = {
    "BDIO_CORE_ADDRESS": deployments.BDIOCoreRegistry,
    "ACCESS_CONTROL_ADDRESS": deployments.AccessControlManager,
    "ENDORSEMENT_MANAGER_ADDRESS": deployments.EndorsementManager,
    "EXPIRY_MANAGER_ADDRESS": deployments.BDIOExpiryManager,
    "VC_MANAGER_ADDRESS": deployments.VCManager
  };

  // Also add the original format for backward compatibility
  const legacyMappings = {
    "BDIOCoreRegistry": deployments.BDIOCoreRegistry,
    "AccessControlManager": deployments.AccessControlManager,
    "EndorsementManager": deployments.EndorsementManager,
    "BDIOExpiryManager": deployments.BDIOExpiryManager,
    "VCManager": deployments.VCManager
  };

  console.log("\n📝 Updating contract addresses...");

  // Update main contract addresses
  Object.entries(contractMappings).forEach(([envVar, address]) => {
    const regex = new RegExp(`^${envVar}=.*$`, "m");
    if (envContent.match(regex)) {
      envContent = envContent.replace(regex, `${envVar}=${address}`);
      console.log(`✅ Updated ${envVar}=${address}`);
    } else {
      envContent += `\n${envVar}=${address}`;
      console.log(`➕ Added ${envVar}=${address}`);
    }
  });

  // Update legacy format addresses
  Object.entries(legacyMappings).forEach(([contractName, address]) => {
    const regex = new RegExp(`^${contractName}:.*$`, "m");
    if (envContent.match(regex)) {
      envContent = envContent.replace(regex, `${contractName}: ${address}`);
      console.log(`✅ Updated ${contractName}: ${address}`);
    } else {
      // Add legacy format if not exists
      envContent += `\n${contractName}: ${address}`;
      console.log(`➕ Added ${contractName}: ${address}`);
    }
  });

  // Add deployment metadata
  const metadataSection = `
# Deployment Metadata (Auto-generated)
DEPLOYMENT_NETWORK=${deployments.network}
DEPLOYMENT_CHAIN_ID=${deployments.chainId}
DEPLOYMENT_DATE=${deployments.deployedAt || deployments.updatedAt}
DEPLOYER_ADDRESS=${deployments.deployer || 'Unknown'}`;

  // Remove existing metadata section if present
  envContent = envContent.replace(/\n# Deployment Metadata \(Auto-generated\)[\s\S]*?(?=\n[A-Z_]+=|$)/g, "");
  
  // Add new metadata section
  envContent += metadataSection;

  // Write updated .env file
  fs.writeFileSync(envPath, envContent);

  console.log("\n✅ .env file updated successfully!");
  console.log("\n📋 Updated Variables:");
  Object.entries(contractMappings).forEach(([envVar, address]) => {
    console.log(`${envVar}=${address}`);
  });

  console.log("\n📋 Deployment Info Added:");
  console.log(`DEPLOYMENT_NETWORK=${deployments.network}`);
  console.log(`DEPLOYMENT_CHAIN_ID=${deployments.chainId}`);
  console.log(`DEPLOYMENT_DATE=${deployments.deployedAt || deployments.updatedAt}`);
  console.log(`DEPLOYER_ADDRESS=${deployments.deployer || 'Unknown'}`);

  console.log(`\n💾 Backup saved as: ${path.basename(backupPath)}`);
  
  // Show next steps
  console.log("\n📋 Next Steps:");
  console.log("1. Verify the updated .env file");
  console.log("2. Update your application configuration");
  console.log("3. Test contract interactions with new addresses");
  console.log("4. Commit changes to version control (excluding .env if it contains secrets)");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Failed to update .env file:", error);
    process.exit(1);
  });
