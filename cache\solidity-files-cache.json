{"_format": "hh-sol-cache-2", "files": {"C:\\xampp\\htdocs\\blockstamp\\v6\\contracts\\AccessControlManager.sol": {"lastModificationDate": 1752332209947, "contentHash": "5146a7f3edc866506b544d3db5cee04d", "sourceName": "contracts/AccessControlManager.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["AccessControlManager", "IBDIORegistry", "IEndorsementManager"]}, "C:\\xampp\\htdocs\\blockstamp\\v6\\contracts\\BDIOCoreRegistry.sol": {"lastModificationDate": 1752341705305, "contentHash": "624c06a93cd54e13b15a1231ea99bd29", "sourceName": "contracts/BDIOCoreRegistry.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol", "@openzeppelin/contracts/utils/Strings.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["BDIOCoreRegistry"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\Strings.sol": {"lastModificationDate": 1752328451820, "contentHash": "48686fc32a22a3754b8e63321857dd2a", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Strings"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\ERC721Enumerable.sol": {"lastModificationDate": 1752328451411, "contentHash": "212e86880d210b6cac8c3eb0cd6b1fad", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC721.sol", "./IERC721Enumerable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC721Enumerable"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\math\\Math.sol": {"lastModificationDate": 1752328451751, "contentHash": "fe63409d8a06818b926cf89e0ea88b1b", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Math"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SignedMath.sol": {"lastModificationDate": 1752328451808, "contentHash": "9488ebd4daacfee8ad04811600d7d061", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SignedMath"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\ERC721.sol": {"lastModificationDate": 1752328451403, "contentHash": "95602b04f0b53f1139f4668d123ddeb7", "sourceName": "@openzeppelin/contracts/token/ERC721/ERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC721.sol", "./IERC721Receiver.sol", "./extensions/IERC721Metadata.sol", "../../utils/Address.sol", "../../utils/Context.sol", "../../utils/Strings.sol", "../../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC721"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\IERC721Enumerable.sol": {"lastModificationDate": 1752328451664, "contentHash": "f4dd06783f321aa26179bc2b6e361c29", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721Enumerable"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1752328451268, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Context"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\Address.sol": {"lastModificationDate": 1752328451246, "contentHash": "211ffd288c1588ba8c10eae668ca3c66", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["Address"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol": {"lastModificationDate": 1752328451345, "contentHash": "0e7db055ce108f9da7bb6686a00287c0", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC165"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721Receiver.sol": {"lastModificationDate": 1752328451689, "contentHash": "c22d4395e33763de693fd440c6fd10e1", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721Receiver"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721.sol": {"lastModificationDate": 1752328451660, "contentHash": "48de4c9a3a4ae5ef66a2aa620843413f", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\extensions\\IERC721Metadata.sol": {"lastModificationDate": 1752328451681, "contentHash": "efbc0d15b80a74e34dbe8da0f3e879bb", "sourceName": "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC721.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721Metadata"]}, "C:\\xampp\\htdocs\\blockstamp\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol": {"lastModificationDate": 1752328451571, "contentHash": "03e6768535ac4da0e9756f1d8a4a018a", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC165"]}, "C:\\xampp\\htdocs\\blockstamp\\v6\\contracts\\BDIOExpiryManager.sol": {"lastModificationDate": 1752074801804, "contentHash": "44748a1755d2abbf3b893fe10d99bbd5", "sourceName": "contracts/BDIOExpiryManager.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["BDIOExpiryManager", "IBDIORegistry"]}, "C:\\xampp\\htdocs\\blockstamp\\v6\\contracts\\EndorsementManager.sol": {"lastModificationDate": 1752316783479, "contentHash": "f459665174b345d289e389bc67738191", "sourceName": "contracts/EndorsementManager.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["EndorsementManager", "IAccessControlManager", "IBDIORegistry"]}, "C:\\xampp\\htdocs\\blockstamp\\v6\\contracts\\VCManager.sol": {"lastModificationDate": 1752075377239, "contentHash": "1b80d97acfad34d382c22a45e94a6400", "sourceName": "contracts/VCManager.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IBDIORegistry", "VCManager"]}}}