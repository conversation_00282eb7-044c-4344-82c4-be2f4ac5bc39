const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  // Ensure we're on Polygon mainnet
  if (hre.network.name !== "polygon") {
    console.error("❌ This script is specifically for Polygon mainnet verification.");
    console.error(`Current network: ${hre.network.name}`);
    console.error("Please run: npx hardhat run scripts/verify-polygon-mainnet.js --network polygon");
    process.exit(1);
  }

  console.log("🔍 Starting Polygon Mainnet Contract Verification...");
  console.log("🌐 Network:", hre.network.name);
  console.log("⛽ Chain ID:", hre.network.config.chainId);

  // Check if we have the required API key
  if (!process.env.POLYGONSCAN_API_KEY) {
    console.error("❌ POLYGONSCAN_API_KEY not found in .env file");
    console.error("Please add your PolygonScan API key to continue");
    process.exit(1);
  }

  const deploymentsPath = path.join(__dirname, "..", "deployments.json");
  if (!fs.existsSync(deploymentsPath)) {
    console.error("❌ deployments.json not found. Please deploy contracts first.");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync(deploymentsPath));
  
  // Verify deployment is for Polygon mainnet
  if (deployments.network !== "polygon") {
    console.error(`❌ Deployment file is for ${deployments.network}, not polygon mainnet`);
    console.error("Please deploy to polygon mainnet first");
    process.exit(1);
  }

  console.log(`📋 Verifying contracts deployed on ${deployments.network}`);
  console.log(`📅 Deployed at: ${deployments.deployedAt || deployments.updatedAt}`);
  console.log(`👤 Deployed by: ${deployments.deployer || 'Unknown'}`);

  console.log("\n🔍 Starting verification process...");
  console.log("⏳ This may take several minutes...");

  let verificationResults = {
    successful: [],
    failed: [],
    alreadyVerified: []
  };

  try {
    // Verify each contract
    await verifyContract("BDIOCoreRegistry", deployments.BDIOCoreRegistry, [], verificationResults);
    
    await verifyContract("AccessControlManager", deployments.AccessControlManager, [
      deployments.BDIOCoreRegistry,
      "0x0000000000000000000000000000000000000000"  // dummy address used at deploy
    ], verificationResults);
    
    await verifyContract("EndorsementManager", deployments.EndorsementManager, [
      deployments.AccessControlManager,
      deployments.BDIOCoreRegistry
    ], verificationResults);
    
    await verifyContract("BDIOExpiryManager", deployments.BDIOExpiryManager, [
      deployments.BDIOCoreRegistry
    ], verificationResults);
    
    await verifyContract("VCManager", deployments.VCManager, [
      deployments.BDIOCoreRegistry
    ], verificationResults);

    // Print summary
    console.log("\n📊 VERIFICATION SUMMARY");
    console.log("========================");
    
    if (verificationResults.successful.length > 0) {
      console.log(`✅ Successfully verified (${verificationResults.successful.length}):`);
      verificationResults.successful.forEach(contract => {
        console.log(`   - ${contract}`);
      });
    }
    
    if (verificationResults.alreadyVerified.length > 0) {
      console.log(`⚠️  Already verified (${verificationResults.alreadyVerified.length}):`);
      verificationResults.alreadyVerified.forEach(contract => {
        console.log(`   - ${contract}`);
      });
    }
    
    if (verificationResults.failed.length > 0) {
      console.log(`❌ Failed verification (${verificationResults.failed.length}):`);
      verificationResults.failed.forEach(contract => {
        console.log(`   - ${contract}`);
      });
    }

    console.log("\n🔗 PolygonScan Contract Links:");
    console.log(`📋 BDIOCoreRegistry: https://polygonscan.com/address/${deployments.BDIOCoreRegistry}#code`);
    console.log(`🔐 AccessControlManager: https://polygonscan.com/address/${deployments.AccessControlManager}#code`);
    console.log(`📝 EndorsementManager: https://polygonscan.com/address/${deployments.EndorsementManager}#code`);
    console.log(`⏰ BDIOExpiryManager: https://polygonscan.com/address/${deployments.BDIOExpiryManager}#code`);
    console.log(`📄 VCManager: https://polygonscan.com/address/${deployments.VCManager}#code`);

    if (verificationResults.failed.length === 0) {
      console.log("\n🎉 ALL CONTRACTS VERIFIED SUCCESSFULLY! 🎉");
    } else {
      console.log("\n⚠️  Some contracts failed verification. Check the errors above.");
    }

  } catch (error) {
    console.error("\n❌ VERIFICATION PROCESS FAILED:", error.message);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

async function verifyContract(contractName, address, constructorArgs, results) {
  console.log(`\n🔍 Verifying ${contractName}...`);
  console.log(`📍 Address: ${address}`);
  console.log(`📝 Constructor args: ${JSON.stringify(constructorArgs)}`);
  
  try {
    await hre.run("verify:verify", {
      address,
      constructorArguments: constructorArgs
    });
    
    console.log(`✅ ${contractName} verified successfully!`);
    results.successful.push(contractName);
    
  } catch (error) {
    if (
      error.message &&
      (error.message.includes("Already Verified") ||
       error.message.includes("Contract source code already verified") ||
       error.message.includes("already verified"))
    ) {
      console.log(`⚠️ ${contractName} already verified`);
      results.alreadyVerified.push(contractName);
      
    } else if (error.message && error.message.includes("does not have bytecode")) {
      console.error(`❌ ${contractName}: Contract not found at address ${address}`);
      console.error("Make sure the contract is deployed on Polygon mainnet");
      results.failed.push(`${contractName} (not deployed)`);
      
    } else if (error.message && error.message.includes("Invalid API Key")) {
      console.error(`❌ Invalid PolygonScan API key`);
      console.error("Please check your POLYGONSCAN_API_KEY in .env file");
      results.failed.push(`${contractName} (invalid API key)`);
      
    } else if (error.message && error.message.includes("rate limit")) {
      console.error(`❌ ${contractName}: Rate limited by PolygonScan`);
      console.error("Please wait a few minutes and try again");
      results.failed.push(`${contractName} (rate limited)`);
      
    } else {
      console.error(`❌ ${contractName} verification failed:`);
      console.error("Error:", error.message || error);
      results.failed.push(`${contractName} (${error.message || 'unknown error'})`);
    }
  }
  
  // Add delay between verifications to avoid rate limiting
  console.log("⏳ Waiting 3 seconds before next verification...");
  await new Promise(resolve => setTimeout(resolve, 3000));
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
