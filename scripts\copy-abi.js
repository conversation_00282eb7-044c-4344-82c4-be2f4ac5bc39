const fs = require("fs");
const path = require("path");

async function main() {
  console.log("📦 Copying ABIs to abis/ folder...");

  const contracts = [
    "BDIOCoreRegistry",
    "AccessControlManager",
    "EndorsementManager",
    "BDIOExpiryManager",
    "VCManager"
  ];

  const abiDir = path.join(__dirname, "..", "abis");
  if (!fs.existsSync(abiDir)) {
    fs.mkdirSync(abiDir, { recursive: true });
  }

  for (const name of contracts) {
    const artifactPath = path.join(__dirname, "..", "artifacts", "contracts", `${name}.sol`, `${name}.json`);
    if (!fs.existsSync(artifactPath)) {
      console.warn(`⚠️ Artifact not found: ${artifactPath}`);
      continue;
    }

    const artifact = JSON.parse(fs.readFileSync(artifactPath, "utf8"));
    const abiOnly = { abi: artifact.abi };

    const abiFile = path.join(abiDir, `${name}.json`);
    fs.writeFileSync(abiFile, JSON.stringify(abiOnly, null, 2));
    console.log(`✅ Copied: ${name}.json`);
  }

  console.log("\n🎉 All ABIs copied to abis/ folder!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ copy-abi.js failed:", error);
    process.exit(1);
  });
