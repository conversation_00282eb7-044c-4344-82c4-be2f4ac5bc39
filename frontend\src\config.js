// frontend/src/config.js
import deployments from "./deployments.json";

// Pakai di frontend:
// import { BDIOCORE_ADDRESS, ENDORSEMENT_ADDRESS } from "./config";
// console.log("BDIOCore contract at:", BDIOCORE_ADDRESS);

export const BDIOCORE_ADDRESS = deployments.BDIOCoreRegistry;
export const ACCESSCONTROL_ADDRESS = deployments.AccessControlManager;
export const ENDORSEMENT_ADDRESS = deployments.EndorsementManager;
export const CATEGORY_ADDRESS = deployments.CategoryManager;
export const METADATA_ADDRESS = deployments.MetadataManager;
export const VCMANAGER_ADDRESS = deployments.VCManager;
export const NFTWRAPPER_ADDRESS = deployments.NFTWrapper;