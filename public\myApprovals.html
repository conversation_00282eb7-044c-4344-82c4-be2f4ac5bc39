<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>📄 My Approved Documents - BDIOCoreRegistry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f9fafb;
    color: #333;
    max-width: 960px;
    margin: 2em auto;
    padding: 2em;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    background-color: #fff;
  }
  h2 {
    text-align: center;
    margin-bottom: 1em;
    color: #1e40af;
  }
  button {
    padding: 0.6em 1.2em;
    background: #1e40af;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
    margin-bottom: 1em;
  }
  button:hover { background: #1a3691; }
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1em;
  }
  th, td {
    border: 1px solid #ddd;
    padding: 0.8em;
    text-align: left;
  }
  th {
    background-color: #1e40af;
    color: white;
  }
  tr:nth-child(even) {
    background-color: #f1f5f9;
  }
  #status {
    margin-top: 1em;
    padding: 0.8em;
    background: #f1f5f9;
    border-left: 4px solid #1e40af;
    font-size: 0.95em;
    white-space: pre-wrap;
    border-radius: 4px;
    min-height: 3em;
  }
</style>
</head>
<body>

<h2>📄 Documents You Are Approved To Sign</h2>

<button id="connectWalletBtn" onclick="connectWallet()">Connect Wallet</button>

<div id="userAddress" style="margin-bottom: 1em;"></div>

<table id="approvedDocumentsTable" style="display:none;">
  <thead>
    <tr>
      <th>BDIO ID</th>
      <th>Title</th>
      <th>Category</th>
      <th>Active</th>
      <th>Archived</th>
      <th>Created At</th>
      <th>Details</th>
      <th>Action</th>
    </tr>
  </thead>
  <tbody id="approvedDocumentsBody"></tbody>
</table>

<div id="status">Please connect your wallet to view your approved documents.</div>

<script>
const bdioCoreAddress = '******************************************';
const accessControlAddress = '******************************************';
const endorsementAddress = '******************************************';

const minimalAccessControlAbi = [
  "function isSignerApproved(string bdioId, address signer) public view returns (bool)",
  "function getApprovedSigners(string bdioId) public view returns (address[])"
];

const minimalEndorsementAbi = [
  "function getEndorsementsCount(string bdioId) public view returns (uint256)",
  "function getEndorsementByIndex(string bdioId, uint256 index) public view returns (address signer, bytes signature, uint256 timestamp, string note)"
];

let provider, signer, contract, accessControlContract, endorsementManager, contractAbi;
let userAddress = null;

async function loadABI() {
  try {
    const res = await fetch('abis/BDIOCoreRegistry.json');
    const json = await res.json();
    contractAbi = json.abi;
  } catch (e) {
    log('❌ Failed to load ABI: ' + e);
  }
}

async function init() {
  await loadABI();
  if (window.ethereum) {
    provider = new ethers.BrowserProvider(window.ethereum);
    window.ethereum.on('accountsChanged', async (accounts) => {
      if (accounts.length === 0) {
        userAddress = null;
        document.getElementById('userAddress').textContent = '';
        document.getElementById('connectWalletBtn').style.display = 'inline-block';
        document.getElementById('approvedDocumentsTable').style.display = 'none';
        document.getElementById('approvedDocumentsBody').innerHTML = '';
        log('⚠️ MetaMask is locked or no accounts available. Please connect your wallet.');
      } else {
        userAddress = accounts[0];
        document.getElementById('userAddress').textContent = 'Connected as: ' + userAddress;
        document.getElementById('connectWalletBtn').style.display = 'none';
        contract = new ethers.Contract(bdioCoreAddress, contractAbi, provider);
        accessControlContract = new ethers.Contract(accessControlAddress, minimalAccessControlAbi, provider);
        endorsementManager = new ethers.Contract(endorsementAddress, minimalEndorsementAbi, provider);
        await loadApprovedDocuments();
        log('ℹ️ Account changed to ' + userAddress);
      }
    });
  } else {
    log('❌ MetaMask not found. Please install.');
  }
}

async function connectWallet() {
  try {
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();
    userAddress = await signer.getAddress();
    document.getElementById('userAddress').textContent = 'Connected as: ' + userAddress;
    document.getElementById('connectWalletBtn').style.display = 'none';
    contract = new ethers.Contract(bdioCoreAddress, contractAbi, provider);
    accessControlContract = new ethers.Contract(accessControlAddress, minimalAccessControlAbi, provider);
    endorsementManager = new ethers.Contract(endorsementAddress, minimalEndorsementAbi, provider);
    await loadApprovedDocuments();
  } catch (e) {
    log('❌ Error connecting wallet: ' + (e?.message || e));
  }
}

const DB_NAME = 'myApprovalsCacheDB';
const DB_VERSION = 1;
const STORE_NAME = 'documents';

function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, { keyPath: 'bdioId' });
      }
    };
  });
}

async function getCachedDocuments() {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const tx = db.transaction(STORE_NAME, 'readonly');
    const store = tx.objectStore(STORE_NAME);
    const request = store.getAll();
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

async function cacheDocuments(docs) {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const tx = db.transaction(STORE_NAME, 'readwrite');
    const store = tx.objectStore(STORE_NAME);
    docs.forEach(doc => store.put(doc));
    tx.oncomplete = () => resolve();
    tx.onerror = () => reject(tx.error);
  });
}

async function loadApprovedDocuments() {
  try {
    // Clear cache for previous user on account change
    await clearCache();

    const cachedDocs = await getCachedDocuments();
    if (cachedDocs.length > 0) {
      renderDocuments(cachedDocs);
      log('✅ Loaded documents from cache.');
      return;
    }

    const allCount = await contract.totalSupply();
    const docs = [];

    const promises = [];
    for (let i = 0; i < allCount; i++) {
      promises.push((async () => {
        const tokenId = await contract.tokenByIndex(i);
        const bdioId = await contract.tokenIdToBdio(tokenId);
        if (!bdioId) return;

        const approved = await accessControlContract.isSignerApproved(bdioId, userAddress);
        if (!approved) return;

        const docBasic = await contract.verifyDocument(bdioId);
        const createdAt = new Date(Number(docBasic[3]) * 1000).toLocaleString();

        // Get endorsements count and signed addresses
        const count = await endorsementManager.getEndorsementsCount(bdioId);
        const signedAddresses = new Set();
        for (let j = 0; j < count; j++) {
          const e = await endorsementManager.getEndorsementByIndex(bdioId, j);
          signedAddresses.add(e[0].toLowerCase());
        }

        const isSigned = signedAddresses.has(userAddress.toLowerCase());

        docs.push({
          bdioId,
          title: docBasic[5],
          category: docBasic[5],
          active: docBasic[1],
          archived: docBasic[2],
          createdAt,
          isSigned
        });
      })());
    }

    await Promise.all(promises);

    await cacheDocuments(docs);
    renderDocuments(docs);
    log('✅ Loaded your approved documents.');
  } catch (e) {
    log('❌ Error loading approved documents: ' + (e?.message || e));
  }
}

async function clearCache() {
  const db = await openDB();
  return new Promise((resolve, reject) => {
    const tx = db.transaction(STORE_NAME, 'readwrite');
    const store = tx.objectStore(STORE_NAME);
    const request = store.clear();
    request.onsuccess = () => resolve();
    request.onerror = () => reject(request.error);
  });
}

function renderDocuments(docs) {
  const tbody = document.getElementById('approvedDocumentsBody');
  tbody.innerHTML = '';
  for (const doc of docs) {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${doc.bdioId}</td>
      <td>${doc.title}</td>
      <td>${doc.category}</td>
      <td>${doc.active}</td>
      <td>${doc.archived}</td>
      <td>${doc.createdAt}</td>
      <td><a href="get.html?bdioId=${encodeURIComponent(doc.bdioId)}" target="_blank">View</a></td>
      <td>
        ${doc.isSigned 
          ? '<button style="background-color: green; color: white; cursor: default;" disabled>Signed</button>' 
          : `<a href="endorse.html?bdioId=${encodeURIComponent(doc.bdioId)}"><button>Need Sign</button></a>`}
      </td>
    `;
    tbody.appendChild(tr);
  }
  document.getElementById('approvedDocumentsTable').style.display = docs.length > 0 ? 'table' : 'none';
}

function log(msg) {
  document.getElementById('status').innerText = msg;
}

window.onload = init;
</script>

</body>
</html>
