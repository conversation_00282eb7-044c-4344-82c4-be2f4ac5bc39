<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>➕ Add Document Version</title>
<style>
  body { font-family: 'Segoe UI', sans-serif; max-width: 600px; margin: 40px auto; }
  input, button { width: 100%; padding: 10px; margin: 8px 0; }
  #log { background: #f9fafb; border: 1px solid #ddd; padding: 10px; font-size: 0.9em; white-space: pre-wrap; height: 200px; overflow-y: auto; }
</style>
</head>
<body>
<h2>➕ Add Document Version</h2>

<input id="bdioId" placeholder="BDIO ID (e.g., 6fd49df8c)" />
<input id="newHash" placeholder="New Hash (auto-filled after file upload)" />
<input id="note" placeholder="Note (e.g., Version 2)" />
<input type="file" id="fileInput" />
<button onclick="addVersion()">Add Version</button>

<h3>Debug Log:</h3>
<div id="log"></div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script>
const contractAddress = '******************************************'; // ganti dengan yg paling baru
const abi = [
  {
    "inputs":[{"internalType":"string","name":"bdioId","type":"string"},{"internalType":"string","name":"newHash","type":"string"},{"internalType":"string","name":"note","type":"string"},{"internalType":"string","name":"txHash","type":"string"}],
    "name":"addVersion","outputs":[],"stateMutability":"nonpayable","type":"function"
  },
  {
    "inputs":[{"internalType":"string","name":"bdioId","type":"string"}],
    "name":"getDocumentOwner","outputs":[{"internalType":"address","name":"","type":"address"}],
    "stateMutability":"view","type":"function"
  }
];

let provider, signer, contract;

async function connect() {
  if (!window.ethereum) { log('❌ MetaMask not detected'); return; }
  provider = new ethers.BrowserProvider(window.ethereum);
  await provider.send("eth_requestAccounts", []);
  signer = await provider.getSigner();
  contract = new ethers.Contract(contractAddress, abi, signer);
  log('✅ Connected as: ' + await signer.getAddress());
}

async function addVersion() {
  const bdioId = document.getElementById('bdioId').value.trim();
  const newHash = document.getElementById('newHash').value.trim();
  const note = document.getElementById('note').value.trim();

  if (!bdioId || !newHash || !note) {
    log('⚠️ Please fill all fields');
    return;
  }

  try {
    const myAddr = await signer.getAddress();
    const owner = await contract.getDocumentOwner(bdioId);
    log(`👤 Your address: ${myAddr}\n📄 Document owner: ${owner}`);

    if (myAddr.toLowerCase() !== owner.toLowerCase()) {
      log('❌ You are NOT the owner of this document.');
      return;
    }

    log('📡 Sending addVersion transaction...');
    const tx = await contract.addVersion(bdioId, newHash, note, '');
    log('⏳ Tx sent: ' + tx.hash);

    const receipt = await tx.wait();
    log(`✅ Version added in block ${receipt.blockNumber}`);
  } catch (e) {
    console.error(e);
    log('❌ Error: ' + (e?.reason || e?.message || JSON.stringify(e)));
  }
}

// File → compute hash
document.getElementById('fileInput').addEventListener('change', async (e) => {
  const file = e.target.files[0];
  if (!file) { log('⚠️ No file selected'); return; }

  try {
    const buffer = await file.arrayBuffer();
    const hash = ethers.keccak256(new Uint8Array(buffer));
    document.getElementById('newHash').value = hash;
    log('🔑 File hash computed: ' + hash);
  } catch (err) {
    console.error(err);
    log('❌ Error computing hash: ' + (err?.message || err));
  }
});

function log(msg) {
  const el = document.getElementById('log');
  el.textContent += msg + '\n';
  el.scrollTop = el.scrollHeight;
}

connect();
</script>
</body>
</html>
