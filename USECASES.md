# 📦 BDIO Smart Registry Modular – Use Cases & Features

Modular smart contract untuk manajemen dokumen digital, mendukung registrasi, versi, endorsement, metadata, verifiable credentials (VC) dan NFT ownership.

---

## 📦 1️⃣ BDIOCoreRegistry.sol

### ✨ Fungsi utama (core):
- Registrasi dokumen (single & batch)
- Menyimpan hash, judul, pemilik, status aktif/arsip
- Menambah versi baru (versioning)
- Transfer kepemilikan dokumen

✅ **Kenapa penting?**  
Jantung sistem: menyimpan identitas unik setiap dokumen dan historinya.

✅ **Use case:**
- Register dokumen kontrak digital, sertifikat, ijazah
- Simpan riwayat revisi (versioning)
- Transfer kepemilikan digital object (misalnya sertifikat buyer → seller)
- Audit trail & histori revisi

📌 **Contoh nyata:**
- Sertifikat kepemilikan tanah
- Akta pendirian perusahaan
- I<PERSON><PERSON>h perguruan tinggi
- Sertifikat produk halal

---

## 🛡 2️⃣ AccessControlManager.sol

### ✨ Fungsi:
- Menetapkan / mencabut editor
- Menetapkan / mencabut signer (pihak yang boleh endorse)
- Cek status editor / signer

✅ **Kenapa penting?**  
Memberi fleksibilitas: owner bisa menunjuk pihak lain (notaris, pengacara) untuk mengedit atau meng-endorse dokumen.

✅ **Use case:**
- Pemilik dokumen menunjuk notaris untuk menandatangani dokumen
- Revoking: cabut akses editor / signer saat terjadi konflik atau selesai kontrak

📌 **Contoh nyata:**
- Perusahaan menunjuk karyawan sebagai editor kontrak
- Bank menunjuk notaris sebagai signer dokumen agunan

---

## ✍️ 3️⃣ EndorsementManager.sol

### ✨ Fungsi:
- Menyimpan endorsement (signature, issuer, note, timestamp)
- Opsional: verifikasi signature on-chain

✅ **Kenapa penting?**  
Bukti digital bahwa dokumen telah direview dan disetujui pihak berwenang.

✅ **Use case:**
- Dokumen kontrak diendorse pengacara
- Ijazah disahkan dinas pendidikan
- Invoice diendorse oleh CFO

📌 **Contoh nyata:**
- Verifikasi sertifikat pelatihan oleh lembaga akreditasi

---

## 🏷 4️⃣ CategoryManager.sol

### ✨ Fungsi:
- Mengelompokkan dokumen ke kategori (contoh: “Kontrak”, “Invoice”, “HR”)
- Menambah tag / label dokumen

✅ **Kenapa penting?**  
Memudahkan organisasi, pencarian, dan filter dokumen.

✅ **Use case:**
- Dokumen legal dikategorikan “Contract”
- Dokumen keuangan → “Invoice”
- Tag: “Urgent”, “Confidential”, “For Audit”

📌 **Contoh nyata:**
- Dashboard admin menampilkan jumlah dokumen per kategori
- Filter dokumen “Expired soon” atau “Pending endorsement”

---

## 📄 5️⃣ MetadataManager.sol

### ✨ Fungsi:
- Simpan metadata: signedHash, pdfHash, metadataUri, proofUri
- Atur masa berlaku dokumen (expiry)
- Atur visibilitas dokumen (public/private)

✅ **Kenapa penting?**  
Data tambahan untuk validasi, compliance, dan kontrol akses.

✅ **Use case:**
- Simpan hash PDF asli → bukti otentik
- metadataUri → link detail JSON/IPFS
- Set expiry: dokumen berlaku sampai 31/12/2025
- Dokumen internal → set isPublic=false

📌 **Contoh nyata:**
- Sertifikat ISO berlaku 3 tahun
- Dokumen HR hanya internal

---

## 🪪 6️⃣ VCManager.sol

### ✨ Fungsi:
- Menyimpan hash atau URI Verifiable Credential (VC)

✅ **Kenapa penting?**  
Integrasi standar W3C VC – interoperabilitas dengan Web3 & ekosistem credential.

✅ **Use case:**
- Ijazah digital → disimpan sebagai VC
- KTP digital, sertifikat profesional → sebagai VC

📌 **Contoh nyata:**
- Pemilik ijazah share VC ke HR perusahaan
- Karyawan verifikasi sertifikat di wallet Web3

---

## 🖼 7️⃣ NFTWrapper.sol

### ✨ Fungsi:
- Mint NFT ERC-721 untuk setiap dokumen

✅ **Kenapa penting?**  
Proof of ownership yang mudah digunakan di wallet atau marketplace.

✅ **Use case:**
- Dokumen premium atau sertifikat kepemilikan → NFT unik
- NFT bukti kepemilikan di Web3

📌 **Contoh nyata:**
- Sertifikat barang antik, karya seni → NFT
- NFT sertifikat keanggotaan eksklusif

---

## 🧩 ✨ Kenapa modular?
- Mudah upgrade (contoh: tambah endorseWithNote, revokeEditor)
- Scalable: upgrade endorsement system tanpa ubah core
- Hemat gas: core tetap kecil
- Enterprise-ready: fitur dapat dikustom sesuai kebutuhan

---

## ✅ **Singkatnya:**

| Modul                  | Fungsi utama                     | Use case real                        |
|-----------------------|----------------------------------|-------------------------------------|
| BDIOCoreRegistry      | Register, transfer, version     | Semua dokumen digital penting       |
| AccessControlManager  | Approve/revoke editor/signer    | Notaris, auditor, karyawan          |
| EndorsementManager    | Simpan endorsement              | Pengesahan dokumen                  |
| CategoryManager      | Group & tagging                  | Filter, dashboard                   |
| MetadataManager      | Hash, expiry, visibility        | Audit, compliance                   |
| VCManager            | VC hash/URI                      | Web3 credential                     |
| NFTWrapper           | NFT ERC-721                      | Bukti ownership                     |

---

> 📌 *BDIO Smart Registry* memudahkan perusahaan, lembaga, dan developer mengelola dokumen digital secara aman, transparan, dan siap untuk Web3. 🚀
