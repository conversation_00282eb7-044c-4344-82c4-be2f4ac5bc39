<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Set Document Expiry - BDIOExpiryManager</title>
<style>
  body { font-family: 'Segoe UI', sans-serif; max-width:600px; margin:2em auto; padding:1em; background:#f9fafb; border-radius:8px; box-shadow:0 4px 12px rgba(0,0,0,0.1); color:#333; }
  h2 { color:#1e40af; text-align:center; margin-bottom:1em; }
  label { display:block; margin-top:1em; font-weight:600; }
  input, textarea, select { width:100%; padding:0.5em; margin-top:0.3em; border:1px solid #ccc; border-radius:4px; }
  button { margin-top:1.5em; padding:0.7em; background:#1e40af; color:#fff; border:none; border-radius:4px; font-weight:600; cursor:pointer; }
  button:hover { background:#1a3691; }
  #status { margin-top:1em; background:#e0e7ff; padding:0.8em; border-left:4px solid #1e40af; border-radius:4px; font-size:0.95em; white-space:pre-wrap; }
</style>
</head>
<body>
<h2>Set Document Expiry</h2>

<label>Mode</label>
<select id="mode">
  <option value="single">Single Document</option>
  <option value="multiple">Multiple Documents</option>
</select>

<div id="singleFields">
  <label>BDIO ID</label>
  <input type="text" id="bdioIdInput" placeholder="Enter BDIO ID" />
  
  <label>Expiry Date and Time (optional, blank = no expiry)</label>
  <input type="datetime-local" id="expiryInput" />
</div>

<div id="multiFields" style="display:none;">
  <label>BDIO IDs (one per line)</label>
  <textarea id="bdioIdsInput" placeholder="bdioId1\nbdioId2\n..."></textarea>
  
  <label>Expiry Date and Time (optional, blank = no expiry)</label>
  <input type="datetime-local" id="batchExpiryInput" />
</div>

<button id="setExpiryBtn">Set Expiry</button>
<div id="status">Ready</div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script>
const expiryManagerAddress = '0xYourContractAddress'; // ganti dengan address kamu
let provider, signer, expiryManager, contractAbi;

async function init() {
  if (!window.ethereum) return updateStatus('❌ MetaMask not found.');

  provider = new ethers.BrowserProvider(window.ethereum);
  signer = await provider.getSigner();

  try {
    const res = await fetch('/abis/BDIOExpiryManager.json'); // pastikan ABI ada
    const json = await res.json();
    contractAbi = json.abi;
    expiryManager = new ethers.Contract(expiryManagerAddress, contractAbi, signer);
    updateStatus('✅ Ready. Connect wallet to proceed.');
  } catch(e) {
    updateStatus('❌ Failed to load ABI: ' + e);
  }

  // toggle fields
  document.getElementById('mode').addEventListener('change', () => {
    const mode = document.getElementById('mode').value;
    document.getElementById('singleFields').style.display = mode==='single' ? '' : 'none';
    document.getElementById('multiFields').style.display = mode==='multiple' ? '' : 'none';
  });
}

function updateStatus(msg) {
  document.getElementById('status').innerText = msg;
}

async function setExpiry() {
  const mode = document.getElementById('mode').value;

  try {
    if (mode==='single') {
      const bdioId = document.getElementById('bdioIdInput').value.trim();
      const expiryInput = document.getElementById('expiryInput').value;
      const expiryTimestamp = expiryInput ? Math.floor(new Date(expiryInput).getTime()/1000) : 0;
      if (!bdioId) return alert('BDIO ID required');

      updateStatus('⏳ Sending tx...');
      const tx = await expiryManager.setExpiry(bdioId, expiryTimestamp);
      await tx.wait();
      updateStatus(`✅ Expiry set for ${bdioId}: ${expiryTimestamp>0 ? new Date(expiryTimestamp*1000).toLocaleString() : 'No expiry'}`);
    }
    else {
      const raw = document.getElementById('bdioIdsInput').value.trim();
      const bdioIds = raw.split('\n').map(s=>s.trim()).filter(s=>s);
      const expiryInput = document.getElementById('batchExpiryInput').value;
      const expiryTimestamp = expiryInput ? Math.floor(new Date(expiryInput).getTime()/1000) : 0;
      if (bdioIds.length===0) return alert('Enter at least one BDIO ID');
      const timestamps = bdioIds.map(()=>expiryTimestamp);

      updateStatus(`⏳ Sending batch tx for ${bdioIds.length} documents...`);
      const tx = await expiryManager.batchSetExpiry(bdioIds, timestamps);
      await tx.wait();
      updateStatus(`✅ Batch expiry set: ${bdioIds.length} documents, expiry=${expiryTimestamp>0 ? new Date(expiryTimestamp*1000).toLocaleString() : 'No expiry'}`);
    }
  } catch(e) {
    console.error(e);
    updateStatus('❌ Error: ' + (e?.reason || e?.message || e));
  }
}

document.getElementById('setExpiryBtn').addEventListener('click', setExpiry);
window.onload = init;
</script>
</body>
</html>
