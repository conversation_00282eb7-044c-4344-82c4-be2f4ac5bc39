# 📦 BDIO Smart Registry - Modular Contracts
Modular smart contract suite for secure, verifiable digital document management.  
Supports versioning, endorsement, category/tagging, metadata, verifiable credentials (VC), and NFT ownership.

---

## ✅ Use Case Summary

| Use Case                                      | Modul Terkait                | Contoh |
|-----------------------------------------------|-----------------------------|-------|
| Register & store documents                    | BDIOCoreRegistry           | Kontrak, invoice, sertifikat |
| Manage editors & signers                      | AccessControlManager       | Menyetujui notaris, pengacara, auditor |
| Document endorsement                          | EndorsementManager        | Dokumen disahkan oleh notaris |
| Categorize & tag documents                     | CategoryManager           | Tag "urgent", kategori "HR" |
| Manage metadata, expiry, visibility           | MetadataManager           | Hash PDF, expiry date, private/public |
| Integrate verifiable credentials (VC)         | VCManager                 | Ijazah, KTP digital |
| NFT proof of ownership                        | NFTWrapper                | Serti<PERSON>kat seni, keanggotaan eksklusif |

---

## 🛠 Modular Contracts & Main Functions

### 📍 BDIOCoreRegistry.sol
*Registry of digital objects (documents).*

| Function | Description |
|--|--|
| `register(bdioId, hashHex, title)` | Register new document |
| `batchRegister(bdioIds[], hashHexes[], titles[])` | Register multiple documents |
| `addVersion(bdioId, newHash, note)` | Add new document version |
| `transferOwnership(bdioId, newOwner)` | Transfer ownership |

**Events:**  
- `Registered`
- `VersionAdded`
- `OwnershipTransferred`

---

### 🛡 AccessControlManager.sol
*Manage editors & signers.*

| Function | Description |
|--|--|
| `approveEditor(bdioId, editor)` | Add editor |
| `revokeEditor(bdioId, editor)` | Remove editor |
| `approveSigner(bdioId, signer)` | Add signer |
| `revokeSigner(bdioId, signer)` | Remove signer |

**Events:**  
- `EditorApproved`
- `EditorRevoked`
- `SignerApproved`
- `SignerRevoked`

---

### ✍️ EndorsementManager.sol
*Endorsements & verification.*

| Function | Description |
|--|--|
| `endorse(bdioId, signature, note)` | Add endorsement with optional note |
| `verifySignature(hash, signature)` | Verify signature on-chain |

**Events:**  
- `Endorsed`

---

### 🏷 CategoryManager.sol
*Document categories & tags.*

| Function | Description |
|--|--|
| `addToCategory(category, bdioId)` | Add document to category |
| `addTag(bdioId, tag)` | Add tag/label to document |

**Events:**  
- `AddedToCategory`
- `Tagged`

---

### 📄 MetadataManager.sol
*Hash, metadata URIs, expiry, and visibility.*

| Function | Description |
|--|--|
| `setSignedHash(bdioId, hash)` | Set signed file hash |
| `setPdfHash(bdioId, hash)` | Set PDF file hash |
| `setMetadataUri(bdioId, uri)` | Link to metadata JSON |
| `setProofUri(bdioId, uri)` | Link to off-chain proof |
| `setExpiry(bdioId, timestamp)` | Set expiry date |
| `setVisibility(bdioId, isPublic)` | Make document public/private |

**Events:**  
- `MetadataUpdated`

---

### 🪪 VCManager.sol
*Verifiable Credentials (VC).*

| Function | Description |
|--|--|
| `registerVC(bdioId, hash)` | Register VC hash/URI |

**Events:**  
- `VCRegistered`

---

### 🖼 NFTWrapper.sol
*ERC-721 NFT proof of ownership.*

| Function | Description |
|--|--|
| `mintNFT(bdioId)` | Mint NFT linked to document |

---

## 🌐 Integration Examples
- Frontend DApp: register new docs, transfer ownership, endorse.
- Backend API: batch register docs, track expiry, update metadata.
- External tools: use NFT to prove ownership, verify VC.

---

## ⚙ How to Deploy & Verify

```bash
# Compile contracts
npx hardhat compile

# Deploy & auto-verify (Mumbai testnet)
npx hardhat run scripts/deploy.js --network mumbai
