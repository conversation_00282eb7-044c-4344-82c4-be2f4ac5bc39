const hre = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("🔍 Starting verification...");

  if (!fs.existsSync("deployments.json")) {
    console.error("❌ deployments.json not found.");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync("deployments.json"));
  const accessControlManagerAddress = deployments.AccessControlManager;
  const bdioCoreRegistryAddress = deployments.BDIOCoreRegistry || process.env.BDIO_CORE_REGISTRY_ADDRESS;

  if (!accessControlManagerAddress || !bdioCoreRegistryAddress) {
    console.error("❌ Missing contract address or constructor args in deployments.json or env.");
    process.exit(1);
  }

  console.log(`📦 Verifying AccessControlManager at: ${accessControlManagerAddress}`);
  await hre.run("verify:verify", {
    address: accessControlManagerAddress,
    constructorArguments: [bdioCoreRegistryAddress],
  });

  console.log("✅ Verification completed!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Verification failed:", error);
    console.error(error.stack);
    process.exit(1);
  });
