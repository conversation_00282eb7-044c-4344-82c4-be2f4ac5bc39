const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("🔍 Starting verification...");
  console.log("🌐 Network:", hre.network.name);
  console.log("⛽ Chain ID:", hre.network.config.chainId);

  const deploymentsPath = path.join(__dirname, "..", "deployments.json");
  if (!fs.existsSync(deploymentsPath)) {
    console.error("❌ deployments.json not found. Please deploy first.");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync(deploymentsPath));

  // Check if deployment is for the current network
  if (deployments.network !== hre.network.name) {
    console.warn(`⚠️ Warning: Deployment was made on ${deployments.network} but you're verifying on ${hre.network.name}`);
    console.log("Do you want to continue? (This might fail if addresses don't exist on this network)");
  }

  console.log(`📋 Verifying contracts deployed on ${deployments.network}:`);
  console.log(`📅 Deployed at: ${deployments.deployedAt || deployments.updatedAt}`);
  console.log(`👤 Deployed by: ${deployments.deployer || 'Unknown'}`);

  // Check if we have the required API key for verification
  if (hre.network.name === "polygon" && !process.env.POLYGONSCAN_API_KEY) {
    console.error("❌ POLYGONSCAN_API_KEY not found in .env file. Verification will fail.");
    process.exit(1);
  }

  console.log("\n🔍 Starting contract verification process...");

  // BDIOCoreRegistry: no constructor args
  console.log("\n📋 Verifying BDIOCoreRegistry...");
  await verifyContract("BDIOCoreRegistry", deployments.BDIOCoreRegistry, []);

  // AccessControlManager: bdioCoreRegistryAddress, endorsementManagerAddress
  console.log("\n🔐 Verifying AccessControlManager...");
  await verifyContract("AccessControlManager", deployments.AccessControlManager, [
    deployments.BDIOCoreRegistry,
    "0x0000000000000000000000000000000000000000"  // dummy address used at deploy
  ]);

  // EndorsementManager: accessControlManagerAddress, bdioCoreRegistryAddress
  console.log("\n📝 Verifying EndorsementManager...");
  await verifyContract("EndorsementManager", deployments.EndorsementManager, [
    deployments.AccessControlManager,
    deployments.BDIOCoreRegistry
  ]);

  // BDIOExpiryManager: bdioCoreRegistryAddress
  console.log("\n⏰ Verifying BDIOExpiryManager...");
  await verifyContract("BDIOExpiryManager", deployments.BDIOExpiryManager, [
    deployments.BDIOCoreRegistry
  ]);

  // VCManager: bdioCoreRegistryAddress
  console.log("\n📄 Verifying VCManager...");
  await verifyContract("VCManager", deployments.VCManager, [
    deployments.BDIOCoreRegistry
  ]);

  console.log("\n✅ All contracts verified successfully!");
  console.log("\n🔗 View your contracts on PolygonScan:");
  console.log(`📋 BDIOCoreRegistry: https://polygonscan.com/address/${deployments.BDIOCoreRegistry}`);
  console.log(`🔐 AccessControlManager: https://polygonscan.com/address/${deployments.AccessControlManager}`);
  console.log(`📝 EndorsementManager: https://polygonscan.com/address/${deployments.EndorsementManager}`);
  console.log(`⏰ BDIOExpiryManager: https://polygonscan.com/address/${deployments.BDIOExpiryManager}`);
  console.log(`📄 VCManager: https://polygonscan.com/address/${deployments.VCManager}`);
}

async function verifyContract(contractName, address, constructorArgs) {
  console.log(`🔍 Verifying ${contractName} at: ${address}`);
  console.log(`📝 Constructor args: ${JSON.stringify(constructorArgs)}`);

  try {
    await hre.run("verify:verify", {
      address,
      constructorArguments: constructorArgs
    });
    console.log(`✅ ${contractName} verified successfully!`);
  } catch (e) {
    if (
      e.message &&
      (e.message.includes("Already Verified") ||
       e.message.includes("Contract source code already verified") ||
       e.message.includes("already verified"))
    ) {
      console.log(`⚠️ ${contractName} already verified: ${address}`);
    } else if (e.message && e.message.includes("does not have bytecode")) {
      console.error(`❌ Contract not found at address: ${address}`);
      console.error("Make sure the contract is deployed on the current network");
    } else if (e.message && e.message.includes("Invalid API Key")) {
      console.error(`❌ Invalid PolygonScan API key. Please check your .env file`);
    } else {
      console.error(`❌ Verification failed for ${contractName}: ${address}`);
      console.error("Error details:", e.message || e);

      // Additional debugging info
      if (hre.network.name === "polygon") {
        console.log(`🔗 Check contract manually: https://polygonscan.com/address/${address}`);
      }
    }
  }

  // Add a small delay between verifications to avoid rate limiting
  await new Promise(resolve => setTimeout(resolve, 2000));
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
