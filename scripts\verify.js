const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🔍 Starting verification...");

  const deploymentsPath = path.join(__dirname, "..", "deployments.json");
  if (!fs.existsSync(deploymentsPath)) {
    console.error("❌ deployments.json not found. Please deploy first.");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync(deploymentsPath));

  // BDIOCoreRegistry: no constructor args
  await verifyContract(deployments.BDIOCoreRegistry, []);

  // AccessControlManager: bdioCoreRegistryAddress, endorsementManagerAddress
  await verifyContract(deployments.AccessControlManager, [
    deployments.BDIOCoreRegistry,
    "0x0000000000000000000000000000000000000000"  // dummy address used at deploy
  ]);

  // EndorsementManager: accessControlManagerAddress, bdioCoreRegistryAddress
  await verifyContract(deployments.EndorsementManager, [
    deployments.AccessControlManager,
    deployments.BDIOCoreRegistry
  ]);

  // BDIOExpiryManager: bdioCoreRegistryAddress
  await verifyContract(deployments.BDIOExpiryManager, [
    deployments.BDIOCoreRegistry
  ]);

  // VCManager: bdioCoreRegistryAddress
  await verifyContract(deployments.VCManager, [
    deployments.BDIOCoreRegistry
  ]);

  console.log("\n✅ All contracts verified successfully!");
}

async function verifyContract(address, constructorArgs) {
  console.log(`\n🔍 Verifying contract at: ${address}`);
  try {
    await hre.run("verify:verify", {
      address,
      constructorArguments: constructorArgs
    });
    console.log(`✅ Verified: ${address}`);
  } catch (e) {
    if (
      e.message &&
      (e.message.includes("Already Verified") ||
       e.message.includes("Contract source code already verified"))
    ) {
      console.log(`⚠️ Already verified: ${address}`);
    } else {
      console.error(`❌ Verification failed for: ${address}`);
      console.error(e.message || e);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
