<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔄 Transfer Ownership - BDIOCoreRegistry</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f9fafb;
    color: #333;
    max-width: 480px;
    margin: 2em auto;
    padding: 2em;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    background-color: #fff;
  }
  h2 {
    text-align: center;
    margin-bottom: 1em;
    color: #1e40af;
  }
  label {
    margin-top: 1em;
    display: block;
    font-weight: 500;
  }
  input[type="text"] {
    width: 100%;
    padding: 0.6em;
    margin-top: 0.3em;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  button {
    width: 100%;
    padding: 0.8em;
    margin-top: 1.5em;
    background: #1e40af;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
  }
  button:hover { background: #1a3691; }
  #status {
    margin-top: 1em;
    padding: 0.8em;
    background: #f1f5f9;
    border-left: 4px solid #1e40af;
    font-size: 0.95em;
    white-space: pre-wrap;
    border-radius: 4px;
    min-height: 3em;
  }
</style>
</head>
<body>

<h2>🔄 Transfer Document Ownership</h2>

<label for="bdioId">BDIO ID *</label>
<input type="text" id="bdioId" placeholder="Enter BDIO ID (e.g. abc123...)" />

<label for="newOwner">New Owner Address *</label>
<input type="text" id="newOwner" placeholder="Enter new owner Ethereum address" />

<button onclick="transferOwnership()">Transfer Ownership</button>

<div id="status">Ready</div>

<script>
const bdioCoreAddress = '******************************************'; // Update with deployed contract address
let provider, signer, contract, contractAbi;

async function loadABI() {
  try {
    const res = await fetch('abis/BDIOCoreRegistry.json');
    const json = await res.json();
    contractAbi = json.abi;
  } catch (e) {
    log('❌ Failed to load ABI: ' + e);
  }
}

async function init() {
  await loadABI();
  if (window.ethereum) {
    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();
    contract = new ethers.Contract(bdioCoreAddress, contractAbi, signer);
    const addr = await signer.getAddress();
    log('✅ Wallet connected: ' + addr);
  } else {
    log('❌ MetaMask not found. Please install.');
  }
}

async function transferOwnership() {
  try {
    const bdioId = document.getElementById('bdioId').value.trim();
    const newOwner = document.getElementById('newOwner').value.trim();

    if (!bdioId || !newOwner) {
      log('⚠️ Please enter BDIO ID and new owner address.');
      return;
    }

    log('📡 Sending transferOwnership transaction...');
    const tx = await contract.transferOwnership(bdioId, newOwner);
    log('⏳ Waiting for confirmation...');
    const receipt = await tx.wait();
    log('✅ Ownership transferred!\nTxHash: ' + receipt.hash);
  } catch (e) {
    log('❌ Error: ' + (e?.message || e));
  }
}

function log(msg) {
  document.getElementById('status').innerText = msg;
}

window.onload = init;
</script>

</body>
</html>
