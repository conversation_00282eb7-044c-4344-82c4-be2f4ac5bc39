{"_format": "hh-sol-artifact-1", "contractName": "IAccessControlManager", "sourceName": "contracts/EndorsementManager.sol", "abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "isSignerApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}