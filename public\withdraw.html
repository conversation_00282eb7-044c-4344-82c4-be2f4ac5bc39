<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>🪙 Withdraw Dashboard (Fixed Contract)</title>
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
</head>
<body class="bg-gray-50 text-gray-800">
<div class="max-w-xl mx-auto mt-10 p-6 bg-white rounded-2xl shadow-xl space-y-6">
  <h2 class="text-2xl font-bold mb-4">🪙 Withdraw POL from Contract</h2>

  <div>
    <p><span class="font-medium">Contract Address:</span>
      <span id="contractAddress" class="text-blue-600 break-all">YOUR_CONTRACT_ADDRESS_HERE</span></p>
  </div>

  <div class="flex items-center justify-between">
    <button onclick="connectAndFetchBalance()"
      class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">
      🔄 Connect & Fetch Balance
    </button>
    <span id="balance" class="font-semibold text-green-600">Balance: -</span>
  </div>

  <hr/>

  <div>
    <button onclick="withdrawAll()"
      class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">
      💰 Withdraw All to Owner
    </button>
  </div>

  <div>
    <label class="block mb-1 font-medium">Target Address (for withdrawTo)</label>
    <input id="toAddress" type="text" placeholder="0x..."
      class="w-full border rounded p-2"/>
  </div>

  <div>
    <label class="block mb-1 font-medium">Amount (in POL)</label>
    <input id="amount" type="number" step="0.01" placeholder="0.1"
      class="w-full border rounded p-2"/>
  </div>

  <div>
    <button onclick="withdrawTo()"
      class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">
      ➡️ Withdraw To Address
    </button>
  </div>

  <pre id="output" class="bg-gray-100 p-3 rounded text-sm"></pre>
</div>

<script>
  // ✅ Ganti ini dengan data kontrak kamu
  const CONTRACT_ADDRESS = "YOUR_CONTRACT_ADDRESS_HERE";
  const CONTRACT_ABI = [
    {
      "inputs":[],"name":"withdrawAll","outputs":[],"stateMutability":"nonpayable","type":"function"
    },
    {
      "inputs":[{"internalType":"address payable","name":"to","type":"address"},
                {"internalType":"uint256","name":"amount","type":"uint256"}],
      "name":"withdrawTo","outputs":[],"stateMutability":"nonpayable","type":"function"
    }
  ];
  let contract, signer, provider;

  async function connectAndFetchBalance() {
    try {
      if (!window.ethereum) throw 'Please install Metamask!';
      provider = new ethers.BrowserProvider(window.ethereum);
      signer = await provider.getSigner();
      contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);

      const balanceWei = await provider.getBalance(CONTRACT_ADDRESS);
      const balancePOL = ethers.formatEther(balanceWei);
      document.getElementById('balance').textContent = 'Balance: ' + balancePOL + ' POL';

      document.getElementById('output').textContent = '✅ Connected! Contract loaded.';
    } catch (e) {
      console.error(e);
      document.getElementById('output').textContent = '❌ Error: ' + (e.message || e);
    }
  }

  async function withdrawAll() {
    try {
      if (!contract) throw 'Connect first!';
      const tx = await contract.withdrawAll();
      document.getElementById('output').textContent = 'Tx sent: ' + tx.hash;
      await tx.wait();
      document.getElementById('output').textContent += '\n✅ Success!';
      await updateBalance();
    } catch (e) {
      console.error(e);
      document.getElementById('output').textContent = '❌ Error: ' + (e.message || e);
    }
  }

  async function withdrawTo() {
    try {
      if (!contract) throw 'Connect first!';
      const to = document.getElementById('toAddress').value.trim();
      const amount = document.getElementById('amount').value.trim();
      if (!to || !amount) throw 'Please enter target address and amount';
      const amountWei = ethers.parseEther(amount);
      const tx = await contract.withdrawTo(to, amountWei);
      document.getElementById('output').textContent = 'Tx sent: ' + tx.hash;
      await tx.wait();
      document.getElementById('output').textContent += '\n✅ Success!';
      await updateBalance();
    } catch (e) {
      console.error(e);
      document.getElementById('output').textContent = '❌ Error: ' + (e.message || e);
    }
  }

  async function updateBalance() {
    if (provider && contract) {
      const balanceWei = await provider.getBalance(CONTRACT_ADDRESS);
      const balancePOL = ethers.formatEther(balanceWei);
      document.getElementById('balance').textContent = 'Balance: ' + balancePOL + ' POL';
    }
  }
</script>
</body>
</html>
