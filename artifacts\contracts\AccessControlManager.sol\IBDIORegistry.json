{"_format": "hh-sol-artifact-1", "contractName": "IBDIORegistry", "sourceName": "contracts/AccessControlManager.sol", "abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getDocumentOwner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}