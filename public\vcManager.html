<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>VC Manager</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f9fafb;
    color: #333;
    max-width: 480px;
    margin: 2em auto;
    padding: 2em;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    background-color: #fff;
  }
  h2 {
    text-align: center;
    margin-bottom: 1em;
    color: #1e40af;
  }
  label {
    margin-top: 1em;
    display: block;
    font-weight: 500;
  }
  input[type="text"], input[type="password"] {
    width: 100%;
    padding: 0.6em;
    margin-top: 0.3em;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  button {
    width: 100%;
    padding: 0.8em;
    margin-top: 1.5em;
    background: #1e40af;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
  }
  button:hover { background: #1a3691; }
  #status {
    margin-top: 1em;
    padding: 0.8em;
    background: #f1f5f9;
    border-left: 4px solid #1e40af;
    font-size: 0.95em;
    white-space: pre-wrap;
    border-radius: 4px;
    min-height: 3em;
  }
</style>
</head>
<body>

<h2>VC Manager</h2>

<div>
  <strong>Connected Wallet:</strong> <span id="walletAddress">Not connected</span><br/>
  <strong>Contract Owner:</strong> <span id="ownerAddress">Loading...</span>
</div>

<hr/>

<h3>Transfer Ownership</h3>
<label for="newOwner">New Owner Address</label>
<input type="text" id="newOwner" placeholder="0x..." />
<button onclick="transferOwnership()">Transfer Ownership</button>

<hr/>

<h3>Register VC</h3>
<label for="bdioId">BDIO ID</label>
<input type="text" id="bdioId" placeholder="Enter BDIO ID" />
<label for="vcHash">VC Hash</label>
<input type="text" id="vcHash" placeholder="Enter VC Hash" />
<button onclick="registerVC()">Register VC</button>

<hr/>

<h3>Query VC Hash</h3>
<label for="queryBdioId">BDIO ID</label>
<input type="text" id="queryBdioId" placeholder="Enter BDIO ID to query" />
<button onclick="queryVCHash()">Get VC Hash</button>
<div id="queryResult"></div>

<div id="status">Ready</div>

<script>
let provider, signer, contract;
const CONTRACT_ADDRESS = '******************************************';
let CONTRACT_ABI = null;

async function loadABI() {
  try {
    const res = await fetch('/abis/VCManager.json');
    const json = await res.json();
    CONTRACT_ABI = json.abi;
  } catch (e) {
    log('❌ Failed to load ABI: ' + e);
  }
}

async function init() {
  await loadABI();
  if (window.ethereum) {
    provider = new ethers.BrowserProvider(window.ethereum);
    await provider.send("eth_requestAccounts", []);
    signer = await provider.getSigner();
    contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);
    const addr = await signer.getAddress();
    document.getElementById('walletAddress').textContent = addr;
    log('✅ Wallet connected: ' + addr);
    await loadOwner();
  } else {
    log('❌ MetaMask not found. Please install.');
  }
}

async function loadOwner() {
  try {
    const owner = await contract.owner();
    document.getElementById('ownerAddress').textContent = owner;
  } catch (e) {
    log('❌ Failed to load owner: ' + e);
  }
}

async function transferOwnership() {
  try {
    const newOwner = document.getElementById('newOwner').value.trim();
    if (!newOwner) {
      log('⚠️ Please enter a new owner address.');
      return;
    }
    log('📡 Sending transferOwnership transaction...');
    const tx = await contract.transferOwnership(newOwner);
    log('⏳ Waiting for confirmation...');
    await tx.wait();
    log('✅ Ownership transferred to ' + newOwner);
    await loadOwner();
  } catch (e) {
    log('❌ Error: ' + (e?.message || e));
  }
}

async function registerVC() {
  try {
    const bdioId = document.getElementById('bdioId').value.trim();
    const vcHash = document.getElementById('vcHash').value.trim();
    if (!bdioId || !vcHash) {
      log('⚠️ Please enter both BDIO ID and VC Hash.');
      return;
    }
    log('📡 Sending registerVC transaction...');
    const tx = await contract.registerVC(bdioId, vcHash);
    log('⏳ Waiting for confirmation...');
    await tx.wait();
    log('✅ VC registered for BDIO ID: ' + bdioId);
  } catch (e) {
    log('❌ Error: ' + (e?.message || e));
  }
}

async function queryVCHash() {
  try {
    const bdioId = document.getElementById('queryBdioId').value.trim();
    if (!bdioId) {
      log('⚠️ Please enter a BDIO ID to query.');
      return;
    }
    log('🔍 Querying VC hash...');
    const hash = await contract.vcHash(bdioId);
    const resultDiv = document.getElementById('queryResult');
    if (hash) {
      resultDiv.textContent = 'VC Hash: ' + hash;
      log('✅ VC hash retrieved.');
    } else {
      resultDiv.textContent = 'No VC hash found for this BDIO ID.';
      log('ℹ️ No VC hash found.');
    }
  } catch (e) {
    log('❌ Error: ' + (e?.message || e));
  }
}

function log(msg) {
  document.getElementById('status').innerText = msg;
}

init();
</script>

</body>
</html>
