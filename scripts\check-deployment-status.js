const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("📊 Checking Deployment Status...");
  console.log("🌐 Current Network:", hre.network.name);
  console.log("⛽ Chain ID:", hre.network.config.chainId);

  const deploymentsPath = path.join(__dirname, "..", "deployments.json");
  if (!fs.existsSync(deploymentsPath)) {
    console.error("❌ deployments.json not found.");
    console.log("📋 Available commands:");
    console.log("  - Deploy to Polygon mainnet: npx hardhat run scripts/deploy-polygon-mainnet.js --network polygon");
    console.log("  - Deploy to testnet: npx hardhat run scripts/deploy.js --network amoy");
    process.exit(1);
  }

  const deployments = JSON.parse(fs.readFileSync(deploymentsPath));
  
  console.log("\n📋 DEPLOYMENT INFORMATION");
  console.log("==========================");
  console.log(`Network: ${deployments.network}`);
  console.log(`Chain ID: ${deployments.chainId}`);
  console.log(`Deployer: ${deployments.deployer || 'Unknown'}`);
  console.log(`Deployed At: ${deployments.deployedAt || deployments.updatedAt}`);

  console.log("\n📍 CONTRACT ADDRESSES");
  console.log("======================");
  console.log(`BDIOCoreRegistry: ${deployments.BDIOCoreRegistry}`);
  console.log(`AccessControlManager: ${deployments.AccessControlManager}`);
  console.log(`EndorsementManager: ${deployments.EndorsementManager}`);
  console.log(`BDIOExpiryManager: ${deployments.BDIOExpiryManager}`);
  console.log(`VCManager: ${deployments.VCManager}`);

  // Check if contracts exist on current network
  if (deployments.network === hre.network.name) {
    console.log("\n🔍 CHECKING CONTRACT STATUS ON CURRENT NETWORK");
    console.log("===============================================");
    
    const provider = hre.ethers.provider;
    const contracts = [
      { name: "BDIOCoreRegistry", address: deployments.BDIOCoreRegistry },
      { name: "AccessControlManager", address: deployments.AccessControlManager },
      { name: "EndorsementManager", address: deployments.EndorsementManager },
      { name: "BDIOExpiryManager", address: deployments.BDIOExpiryManager },
      { name: "VCManager", address: deployments.VCManager }
    ];

    for (const contract of contracts) {
      try {
        const code = await provider.getCode(contract.address);
        if (code === "0x") {
          console.log(`❌ ${contract.name}: No contract found at ${contract.address}`);
        } else {
          console.log(`✅ ${contract.name}: Contract deployed at ${contract.address}`);
        }
      } catch (error) {
        console.log(`⚠️ ${contract.name}: Error checking ${contract.address} - ${error.message}`);
      }
    }
  } else {
    console.log(`\n⚠️ Deployment is for ${deployments.network} but current network is ${hre.network.name}`);
    console.log("Switch networks to check contract status");
  }

  // Show explorer links
  console.log("\n🔗 EXPLORER LINKS");
  console.log("==================");
  
  let explorerBase;
  if (deployments.network === "polygon") {
    explorerBase = "https://polygonscan.com/address/";
  } else if (deployments.network === "amoy") {
    explorerBase = "https://amoy.polygonscan.com/address/";
  } else {
    explorerBase = `https://${deployments.network}.etherscan.io/address/`;
  }

  console.log(`BDIOCoreRegistry: ${explorerBase}${deployments.BDIOCoreRegistry}`);
  console.log(`AccessControlManager: ${explorerBase}${deployments.AccessControlManager}`);
  console.log(`EndorsementManager: ${explorerBase}${deployments.EndorsementManager}`);
  console.log(`BDIOExpiryManager: ${explorerBase}${deployments.BDIOExpiryManager}`);
  console.log(`VCManager: ${explorerBase}${deployments.VCManager}`);

  // Show verification links
  console.log("\n📝 VERIFICATION LINKS (Source Code)");
  console.log("====================================");
  console.log(`BDIOCoreRegistry: ${explorerBase}${deployments.BDIOCoreRegistry}#code`);
  console.log(`AccessControlManager: ${explorerBase}${deployments.AccessControlManager}#code`);
  console.log(`EndorsementManager: ${explorerBase}${deployments.EndorsementManager}#code`);
  console.log(`BDIOExpiryManager: ${explorerBase}${deployments.BDIOExpiryManager}#code`);
  console.log(`VCManager: ${explorerBase}${deployments.VCManager}#code`);

  // Show available commands
  console.log("\n📋 AVAILABLE COMMANDS");
  console.log("=====================");
  
  if (deployments.network === "polygon") {
    console.log("Verify contracts:");
    console.log("  npx hardhat run scripts/verify-polygon-mainnet.js --network polygon");
    console.log("\nOr use the general verify script:");
    console.log("  npx hardhat run scripts/verify.js --network polygon");
  } else {
    console.log("Verify contracts:");
    console.log(`  npx hardhat run scripts/verify.js --network ${deployments.network}`);
  }

  console.log("\nDeploy to different network:");
  console.log("  npx hardhat run scripts/deploy-polygon-mainnet.js --network polygon");
  console.log("  npx hardhat run scripts/deploy.js --network amoy");

  console.log("\nCheck deployment status:");
  console.log("  npx hardhat run scripts/check-deployment-status.js --network <network>");

  // Environment variables check
  console.log("\n🔧 ENVIRONMENT CHECK");
  console.log("====================");
  
  const requiredVars = [
    { name: "PRIVATE_KEY", required: true },
    { name: "POLYGONSCAN_API_KEY", required: deployments.network === "polygon" },
    { name: "RPC_URL", required: false }
  ];

  requiredVars.forEach(envVar => {
    const value = process.env[envVar.name];
    if (value) {
      if (envVar.name === "PRIVATE_KEY") {
        console.log(`✅ ${envVar.name}: Set (${value.substring(0, 6)}...)`);
      } else {
        console.log(`✅ ${envVar.name}: Set`);
      }
    } else {
      if (envVar.required) {
        console.log(`❌ ${envVar.name}: Missing (Required)`);
      } else {
        console.log(`⚠️ ${envVar.name}: Not set (Optional)`);
      }
    }
  });

  console.log("\n✅ Status check complete!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Status check failed:", error);
    process.exit(1);
  });
