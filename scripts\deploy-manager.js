const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Starting deployment...");

  const [deployer] = await hre.ethers.getSigners();
  console.log("👤 Deploying from address:", deployer.address);

  // ✅ Ambil BDIOCoreRegistry address dari env / deployments.json
  let bdioCoreRegistryAddress = process.env.BDIO_CORE_REGISTRY_ADDRESS;
  if (!bdioCoreRegistryAddress && fs.existsSync("deployments.json")) {
    const prev = JSON.parse(fs.readFileSync("deployments.json"));
    bdioCoreRegistryAddress = prev.BDIOCoreRegistry;
  }
  if (!bdioCoreRegistryAddress) {
    console.error("❌ Missing BDIOCoreRegistry address. Set env BDIO_CORE_REGISTRY_ADDRESS or deployments.json");
    process.exit(1);
  }

  // ✅ Deploy AccessControlManager
  const AccessControlManager = await hre.ethers.getContractFactory("AccessControlManager");
  const accessControlManager = await AccessControlManager.deploy(bdioCoreRegistryAddress);
  await accessControlManager.waitForDeployment();
  const accessControlManagerAddress = await accessControlManager.getAddress();
  console.log(`✅ AccessControlManager deployed at: ${accessControlManagerAddress}`);

  // ✅ Simpan ke deployments.json
  let deployments = {};
  if (fs.existsSync("deployments.json")) {
    deployments = JSON.parse(fs.readFileSync("deployments.json"));
  }
  deployments.AccessControlManager = accessControlManagerAddress;
  deployments.updatedAt = new Date().toISOString();
  deployments.network = hre.network.name;

  const filePath = path.join(__dirname, "..", "deployments.json");
  fs.writeFileSync(filePath, JSON.stringify(deployments, null, 2));

  console.log("\n📦 deployments.json saved:");
  console.log(JSON.stringify(deployments, null, 2));

  console.log("\n🎉 AccessControlManager deployed successfully!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    console.error(error.stack);
    process.exit(1);
  });
