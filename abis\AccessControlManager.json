{"abi": [{"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}, {"internalType": "address", "name": "_endorsementManager", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "EndorsementManagerUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "SignerApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}, {"internalType": "address", "name": "", "type": "address"}], "name": "approvedSigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bdioRegistry", "outputs": [{"internalType": "contract IBDIORegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "contractOwner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "endorsementManager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getApprovedSigners", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "getSignerStatus", "outputs": [{"internalType": "bool", "name": "approved", "type": "bool"}, {"internalType": "bool", "name": "signed", "type": "bool"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "isSignerApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "revokeS<PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAddr", "type": "address"}], "name": "setEndorsementManager", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}