{"abi": [{"inputs": [{"internalType": "address", "name": "registryAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "bdioId", "type": "string"}, {"indexed": false, "internalType": "string", "name": "vcHash", "type": "string"}], "name": "VCHashSet", "type": "event"}, {"inputs": [], "name": "bdioRegistry", "outputs": [{"internalType": "contract IBDIORegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "string", "name": "newVCHash", "type": "string"}], "name": "setVCHash", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "vcHash", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "vcHashes", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}]}