const hre = require("hardhat");

async function main() {
  const accessControlManager = "******************************************";

  console.log("Deploying EndorsementManager...");

  const EndorsementManager = await hre.ethers.getContractFactory("EndorsementManager");
  const endorsementManager = await EndorsementManager.deploy(accessControlManager);

  console.log("✅ Deployed EndorsementManager at:", endorsementManager.target);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
