<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🔑 Approve Signer - AccessControlManager</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f9fafb; max-width: 480px; margin: 2em auto; padding: 2em; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); background-color: #fff; color:#333; }
  h2 { text-align: center; color: #1e40af; margin-bottom: 1em; }
  label { margin-top: 1em; display: block; font-weight: 500; }
  input[type="text"] { width: 100%; padding: 0.6em; margin-top: 0.3em; border: 1px solid #ccc; border-radius: 4px; }
  button { width: 100%; padding: 0.8em; margin-top: 0.8em; background: #1e40af; color: #fff; border: none; border-radius: 4px; font-weight: 600; cursor: pointer; transition: background 0.2s; }
  button:hover { background: #1a3691; }
  #status { margin-top: 1em; padding: 0.8em; background: #f1f5f9; border-left: 4px solid #1e40af; font-size: 0.95em; white-space: pre-wrap; border-radius: 4px; min-height: 3em; }
  ul { margin-top: 1em; padding-left: 1.2em; }
</style>
</head>
<body>

<h2>🔑 Approve / Revoke Signer</h2>

<label>BDIO ID *</label>
<input type="text" id="bdioId" placeholder="e.g. abc123..." />

<label>Signer Address *</label>
<input type="text" id="signerAddress" placeholder="e.g. 0x..." />

<button onclick="approveSigner()">✅ Approve Signer</button>
<button onclick="revokeSigner()">🗑️ Revoke Signer</button>
<button onclick="checkApproval()">🔍 Check Approval Status</button>
<button onclick="loadApprovedSigners()">📋 Load All Approved Signers</button>

<div id="status">Ready</div>
<ul id="approvedSignersList"></ul>

<script>
const accessControlAddress = '0x1abfAE2B9b67B30EC092B0ca3D895495C6D32129';
const bdioCoreRegistryAddress = '0xC954fD7aC0cAeaD6BF54e5d9d8123b8D5E87FE6f';

const accessAbi = [
  "function approveSigner(string bdioId, address signer) public",
  "function revokeSigner(string bdioId, address signer) public",
  "function isSignerApproved(string bdioId, address signer) view returns (bool)",
  "function getApprovedSigners(string bdioId) view returns (address[])",
  "function isAdmin(string bdioId, address user) view returns (bool)"
];

const bdioAbi = [
  "function getDocumentOwner(string bdioId) view returns (address)"
];

let provider, signer, accessControl, bdioCore;

async function init() {
  if (!window.ethereum) return log('❌ MetaMask not found. Please install.');
  provider = new ethers.BrowserProvider(window.ethereum);
  await provider.send("eth_requestAccounts", []);
  signer = await provider.getSigner();
  accessControl = new ethers.Contract(accessControlAddress, accessAbi, signer);
  bdioCore = new ethers.Contract(bdioCoreRegistryAddress, bdioAbi, signer);
  log('✅ Wallet connected: ' + await signer.getAddress());
}

async function approveSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');
    log('🔍 Checking ownership...');
    const owner = await bdioCore.getDocumentOwner(bdioId);
    const myAddr = await signer.getAddress();
    if (owner.toLowerCase() !== myAddr.toLowerCase()) return log('❌ You are not the owner.');
    log('📡 Sending approveSigner...');
    const tx = await accessControl.approveSigner(bdioId, addr);
    await tx.wait();
    log('✅ Signer approved! TxHash: ' + tx.hash);
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function revokeSigner() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');
    log('🔍 Checking ownership...');
    const owner = await bdioCore.getDocumentOwner(bdioId);
    const myAddr = await signer.getAddress();
    if (owner.toLowerCase() !== myAddr.toLowerCase()) return log('❌ You are not the owner.');
    log('📡 Sending revokeSigner...');
    const tx = await accessControl.revokeSigner(bdioId, addr);
    await tx.wait();
    log('✅ Signer revoked! TxHash: ' + tx.hash);
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function checkApproval() {
  try {
    const bdioId = getInput('bdioId');
    const addr = getInput('signerAddress');
    const approved = await accessControl.isSignerApproved(bdioId, addr);
    log(`✅ Approval status: ${approved}`);
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function loadApprovedSigners() {
  try {
    const bdioId = getInput('bdioId');
    const list = await accessControl.getApprovedSigners(bdioId);
    const ul = document.getElementById('approvedSignersList');
    ul.innerHTML = '';
    for (let addr of list) {
      const approved = await accessControl.isSignerApproved(bdioId, addr);
      const li = document.createElement('li');
      li.textContent = `${addr} : ${approved ? '✅ Approved' : '❌ Revoked'}`;
      ul.appendChild(li);
    }
    log(`✅ Loaded ${list.length} signer(s).`);
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

function getInput(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) throw '⚠️ Please fill: ' + id;
  return val;
}

function log(msg) {
  document.getElementById('status').innerText = msg;
}

window.onload = init;
</script>

</body>
</html>
