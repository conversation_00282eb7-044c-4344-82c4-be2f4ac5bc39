<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>BDIO Document Detail</title>
<link rel="stylesheet" href="style.css">
</head>
<body>
<h1>📄 BDIO Document Detail</h1>
<input type="text" id="bdioIdInput" placeholder="Enter BDIO ID">
<button id="loadButton" onclick="loadDocument()">Load Document</button>
<span id="loadingSpinner" style="display:none;">Loading...</span>

<div id="documentInfo" style="display:none;">
  <h2>Document Info</h2>
  <p><strong>Title:</strong> <span id="title"></span></p>
  <p><strong>Category:</strong> <span id="category"></span></p>
  <p hidden><strong>Owner:</strong> <span id="owner"></span></p>
  <p><strong>Active:</strong> <span id="active"></span></p>
  <p hidden><strong>Archived:</strong> <span id="archived"></span></p>
  <p><strong>Metadata URI:</strong> <span id="metadataUri"></span></p>
  <p><strong>Blockchain Tx:</strong> <span id="blockchainTxHash"></span></p>
  <p><strong>Initial Hash:</strong> <span id="initialHash"></span></p>
  <p><strong>Created At:</strong> <span id="createdAt"></span></p>
  <p><strong>VC Hash:</strong> <span id="vcHash"></span></p>

  <h3>NFT Info</h3>
  <p><strong>Token ID:</strong> <span id="nftTokenId"></span></p>
  <p><strong>Token URI:</strong> <span id="nftTokenURI"></span></p>

  <h3>Versions</h3>
  <ul id="versions"></ul>

  <h3>Ownership History</h3>
  <ul id="ownershipHistory"></ul>

  <h3>Signers & Status</h3>
  <ul id="signersStatusList">Loading...</ul>

  <h3>Expiry Date</h3>
  <p id="expiryDate">Loading...</p>
  <div id="expiryUpdateDiv" style="display:none;">
    <input type="datetime-local" id="newExpiryInput" />
    <button id="setExpiryButton">Update Expiry</button>
  </div>

  <h3>Endorsements</h3>
  <ul id="endorsements"></ul>
</div>

<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<script>
const bdioCoreAddress='******************************************';
const accessControlAddress='******************************************';
const endorsementAddress='******************************************';
const vcManagerAddress='******************************************';
const expiryManagerAddress='******************************************';

let provider, signer;
let bdioCore, accessControl, endorsementManager, vcManager, expiryManager;
let ownershipListenerAdded=false;

async function init(){
  provider=new ethers.BrowserProvider(window.ethereum);
  signer=await provider.getSigner();

  const [bdioAbi,accessAbi,endAbi,vcAbi,expiryAbi]=await Promise.all([
    fetch('/abis/BDIOCoreRegistry.json').then(r=>r.json()),
    fetch('/abis/AccessControlManager.json').then(r=>r.json()),
    fetch('/abis/EndorsementManager.json').then(r=>r.json()),
    fetch('/abis/VCManager.json').then(r=>r.json()),
    fetch('/abis/BDIOExpiryManager.json').then(r=>r.json()),
  ]);
  bdioCore=new ethers.Contract(bdioCoreAddress,bdioAbi.abi,provider);
  accessControl=new ethers.Contract(accessControlAddress,accessAbi.abi,provider);
  endorsementManager=new ethers.Contract(endorsementAddress,endAbi.abi,provider);
  vcManager=new ethers.Contract(vcManagerAddress,vcAbi.abi,provider);
  expiryManager=new ethers.Contract(expiryManagerAddress,expiryAbi.abi,provider);

  if(!ownershipListenerAdded){
    bdioCore.on('OwnershipTransferred',async(bdioId)=>{
      console.log('OwnershipTransferred:',bdioId);
      await fetch('/api/updateMetadata',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({bdioId})});
      if(document.getElementById('bdioIdInput').value.trim()===bdioId) loadDocument();
    });
    ownershipListenerAdded=true;
  }

  const param=new URLSearchParams(location.search).get('bdioId');
  if(param){ document.getElementById('bdioIdInput').value=param; loadDocument(); }
}

async function loadDocument(){
  const bdioId=document.getElementById('bdioIdInput').value.trim();
  if(!bdioId) return alert('Enter BDIO ID');
  document.getElementById('loadingSpinner').style.display='inline';
  document.getElementById('loadButton').disabled=true;

  try{
    const [owner,active,archived,timestamp,,category]=await bdioCore.verifyDocument(bdioId);
    document.getElementById('owner').textContent=owner;
    document.getElementById('archived').textContent=archived;
    document.getElementById('createdAt').textContent=new Date(Number(timestamp)*1000).toLocaleString();
    document.getElementById('category').textContent=category;

    const metaUrl=`/metadata/${bdioId}.meta.json`;
    document.getElementById('metadataUri').innerHTML=`<a href="${metaUrl}" target="_blank">${metaUrl}</a>`;
    let meta={}; try{meta=await (await fetch(metaUrl)).json();}catch{}

    document.getElementById('blockchainTxHash').innerHTML=meta.blockchaintxHash?`<a href="https://polygonscan.com/tx/${meta.blockchaintxHash}" target="_blank">${meta.blockchaintxHash}</a>`:'N/A';
    document.getElementById('title').textContent=meta.title||'';
    document.getElementById('initialHash').textContent=meta.hashHex||'';

    document.getElementById('vcHash').textContent=await vcManager.vcHash(bdioId)||'N/A';

    const expiry=await expiryManager.getExpiry(bdioId);
    document.getElementById('expiryDate').textContent=expiry>0?new Date(Number(expiry)*1000).toLocaleString():'Not set';
    document.getElementById('active').textContent=active?'Active':(archived?'Archived':'Inactive');

    const versions=await bdioCore.getDocumentVersions(bdioId);
    document.getElementById('versions').innerHTML=versions.map(v=>`<li>${v.hashHex} by ${v.editor} at ${new Date(Number(v.timestamp)*1000).toLocaleString()}</li>`).join('');

    const history=await bdioCore.getOwnershipHistory(bdioId);
    document.getElementById('ownershipHistory').innerHTML=history.map(h=>`<li>${h.owner} - ${h.note} at ${new Date(Number(h.timestamp)*1000).toLocaleString()}</li>`).join('');

    const count=await endorsementManager.getEndorsementsCount(bdioId);
    const ends=[]; for(let i=0;i<count;i++){const e=await endorsementManager.getEndorsementByIndex(bdioId,i); ends.push(`<li>${e[0]} - ${e[3]} at ${new Date(Number(e[2])*1000).toLocaleString()}</li>`);}
    document.getElementById('endorsements').innerHTML=ends.join('');

    // Signers
    const approved=await accessControl.getApprovedSigners(bdioId);
    const signed=new Set(); for(let i=0;i<count;i++){const e=await endorsementManager.getEndorsementByIndex(bdioId,i); signed.add(e[0].toLowerCase());}
    const signerHtml=[]; for(const s of approved){
      const isApproved=await accessControl.isSignerApproved(bdioId,s);
      const status=isApproved?(signed.has(s.toLowerCase())?'Signed':'Awaiting Signature'):'Revoked';
      signerHtml.push(`<li>${s} - ${status}</li>`);
    }
    document.getElementById('signersStatusList').innerHTML=signerHtml.join('');

    await loadNFTInfo(bdioId);

    // Show expiry update if admin
    const isAdmin=await accessControl.isAdmin(bdioId,await signer.getAddress());
    document.getElementById('expiryUpdateDiv').style.display=isAdmin?'block':'none';
    if(isAdmin){
      document.getElementById('setExpiryButton').onclick=async()=>{
        const newExp=Math.floor(new Date(document.getElementById('newExpiryInput').value).getTime()/1000);
        const tx=await expiryManager.connect(signer).setExpiry(bdioId,newExp); await tx.wait();
        await fetch('/api/updateMetadata',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({bdioId})});
        alert('Expiry updated'); loadDocument();
      }
    }

    document.getElementById('documentInfo').style.display='block';
  }catch(e){console.error(e);alert('Failed: '+(e.message||e));}
  finally{
    document.getElementById('loadingSpinner').style.display='none';
    document.getElementById('loadButton').disabled=false;
  }
}

async function loadNFTInfo(bdioId){
  try{
    const tokenId=await bdioCore.bdioToTokenId(bdioId);
    if(tokenId&&tokenId!=0){
      document.getElementById('nftTokenId').textContent=tokenId;
      const uri=await bdioCore.tokenURI(tokenId);
      document.getElementById('nftTokenURI').innerHTML=`<a href="${uri}" target="_blank">${uri}</a>`;
    }else{
      document.getElementById('nftTokenId').textContent='No NFT minted';
      document.getElementById('nftTokenURI').textContent='N/A';
    }
  }catch(e){
    console.error(e);
    document.getElementById('nftTokenId').textContent='Error';
    document.getElementById('nftTokenURI').textContent='Error';
  }
}
window.onload=init;
</script>
</body>
</html>
