const { ethers } = require("hardhat");

async function main() {
  if (process.argv.length < 4) {
    console.error("Usage: node manual-approve.js <BDIO_ID> <SIGNER_ADDRESS>");
    process.exit(1);
  }

  const bdioId = process.argv[2];
  const signerAddress = process.argv[3];

  const deployments = require("../frontend/src/deployments.json");
  const accessControlAddress = deployments.AccessControlManager;

  const [deployer] = await ethers.getSigners();
  console.log("Using deployer address:", deployer.address);

  const AccessControlManager = await ethers.getContractFactory("AccessControlManager");
  const accessControl = AccessControlManager.attach(accessControlAddress);

  console.log(`Approving signer ${signerAddress} for BDIO ID ${bdioId}...`);
  const tx = await accessControl.approveSigner(bdioId, signerAddress);
  await tx.wait();
  console.log("Signer approved successfully.");
}

main().catch((error) => {
  console.error("Error in manual-approve script:", error);
  process.exit(1);
});
