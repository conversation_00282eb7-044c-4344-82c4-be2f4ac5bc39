const ethers  = require('ethers');

// Konfigurasi
const provider = new ethers.JsonRpcProvider("https://polygon-rpc.com"); // ganti RPC sesuai chain kamu
const accessControlAddress = "******************************************";
const endorsementManagerAddress = "******************************************";
const signerAddress = "******************************************";
const bdioId = "2fefb8001a";

const accessControlAbi = [
  "function isSignerApproved(string bdioId, address signer) view returns (bool)"
];
const endorsementManagerAbi = [
  "function accessControlManager() view returns (address)"
];

(async () => {
  const acContract = new ethers.Contract(accessControlAddress, accessControlAbi, provider);
  const emContract = new ethers.Contract(endorsementManagerAddress, endorsementManagerAbi, provider);

  // Cek apakah signer sudah approved
  const isApproved = await acContract.isSignerApproved(bdioId, signerAddress);
  console.log("Signer approved:", isApproved);

  // Cek apakah EndorsementManager pointing ke AccessControlManager yang benar
  const acInEM = await emContract.accessControlManager();
  console.log("EndorsementManager → accessControlManager:", acInEM);

  console.log("Expected AccessControlManager address:", accessControlAddress);
})();
