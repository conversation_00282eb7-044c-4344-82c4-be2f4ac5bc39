// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/utils/Strings.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

contract BDIOCoreRegistry is ERC721Enumerable, ReentrancyGuard, Pausable {
    using Strings for uint256;
    using SafeMath for uint256;

    address public contractOwner;
    address public pendingOwner;
    uint256 public nextTokenId;
    string public baseURI;

    uint256 public registerFee = 0.2 ether;
    uint256 public mintFee = 0.5 ether;
    uint256 public versionFee = 0.2 ether;

    // Rate limiting
    mapping(address => uint256) public lastRegistrationTime;
    uint256 public constant REGISTRATION_COOLDOWN = 1 minutes;

    // Input validation constants
    uint256 public constant MIN_BDIO_ID_LENGTH = 10;
    uint256 public constant MAX_BDIO_ID_LENGTH = 64;
    uint256 public constant HASH_LENGTH = 64;
    uint256 public constant MAX_STRING_LENGTH = 256;
    uint256 public constant MAX_BATCH_SIZE = 50;

    // Emergency controls
    bool public emergencyPaused = false;

    // Global hash registry
    mapping(bytes32 => bool) public registeredHashes;

    struct Version {
        string hashHex;
        uint256 timestamp;
        string note;
        address editor;
        string txHash;
    }

    struct OwnershipHistory {
        address owner;
        uint256 timestamp;
        string note;
    }

    struct Document {
        string hashHex;
        string title;
        string category;
        string metadataUri;
        address owner;
        bool active;
        bool archived;
        uint256 timestamp;
        Version[] versions;
        OwnershipHistory[] history;
    }

    mapping(string => Document) public registry;
    mapping(string => uint256) public documentExpiry;
    mapping(string => uint256) public bdioToTokenId;
    mapping(uint256 => string) public tokenIdToBdio;
    mapping(string => bool) public exists;

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocOwner(string calldata bdioId) {
        require(registry[bdioId].owner == msg.sender, "Not document owner");
        _;
    }

    modifier validBdioId(string calldata bdioId) {
        bytes memory bdioBytes = bytes(bdioId);
        require(
            bdioBytes.length >= MIN_BDIO_ID_LENGTH && 
            bdioBytes.length <= MAX_BDIO_ID_LENGTH, 
            "Invalid BDIO ID length"
        );
        _;
    }

    modifier validHash(string calldata hashHex) {
        require(bytes(hashHex).length == HASH_LENGTH, "Invalid hash length");
        require(_isValidHex(hashHex), "Invalid hex format");
        _;
    }

    modifier validString(string calldata str) {
        require(bytes(str).length <= MAX_STRING_LENGTH, "String too long");
        _;
    }

    modifier rateLimited() {
        require(
            block.timestamp >= lastRegistrationTime[msg.sender] + REGISTRATION_COOLDOWN,
            "Rate limit exceeded"
        );
        _;
    }

    modifier notEmergencyPaused() {
        require(!emergencyPaused, "Emergency paused");
        _;
    }

    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }

    modifier validContract(address addr) {
        require(addr != address(0), "Invalid address");
        require(addr.code.length > 0, "Not a contract");
        _;
    }

    event DocumentRegistered(string indexed bdioId, address indexed owner);
    event OwnershipTransferInitiated(address indexed currentOwner, address indexed newOwner);
    event OwnershipTransferCompleted(address indexed oldOwner, address indexed newOwner);
    event DocumentBatchRegistered(uint count);
    event OwnershipTransferred(string indexed bdioId, address indexed oldOwner, address indexed newOwner);
    event VersionAdded(string indexed bdioId, string newHash, string note, address indexed editor);
    event DocumentArchived(string indexed bdioId, string reason, uint256 timestamp);
    event DocumentDeactivated(string indexed bdioId, string reason, uint256 timestamp);
    event Withdraw(address indexed to, uint256 amount);
    event BaseURIUpdated(string oldBaseURI, string newBaseURI);
    event RegisterFeeUpdated(uint256 oldFee, uint256 newFee);
    event MintFeeUpdated(uint256 oldFee, uint256 newFee);
    event VersionFeeUpdated(uint256 oldFee, uint256 newFee);
    event EmergencyPauseToggled(bool paused);

    constructor() ERC721("BDIONFT", "BDIO") {
        contractOwner = msg.sender;
        baseURI = "https://creden.xyz/metadata/";
    }

    // Emergency controls
    function toggleEmergencyPause() external onlyContractOwner {
        emergencyPaused = !emergencyPaused;
        emit EmergencyPauseToggled(emergencyPaused);
    }

    // Setter functions with event emit
    function setBaseURI(string calldata newBaseURI) external onlyContractOwner validString(newBaseURI) {
        emit BaseURIUpdated(baseURI, newBaseURI);
        baseURI = newBaseURI;
    }

    function setRegisterFee(uint256 newFee) external onlyContractOwner {
        emit RegisterFeeUpdated(registerFee, newFee);
        registerFee = newFee;
    }

    function setMintFee(uint256 newFee) external onlyContractOwner {
        emit MintFeeUpdated(mintFee, newFee);
        mintFee = newFee;
    }

    function setVersionFee(uint256 newFee) external onlyContractOwner {
        emit VersionFeeUpdated(versionFee, newFee);
        versionFee = newFee;
    }

    // Register single document
    function register(
        string calldata bdioId,
        string calldata hashHex,
        string calldata title,
        string calldata category,
        string calldata metadataUri
    ) external payable 
        nonReentrant 
        whenNotPaused 
        notEmergencyPaused
        validBdioId(bdioId)
        validHash(hashHex)
        validString(title)
        validString(category)
        validString(metadataUri)
        rateLimited
    {
        require(!exists[bdioId], "Already exists");
        require(msg.value >= registerFee, "Insufficient register fee");

        // global hash check
        bytes32 hashBytes = keccak256(bytes(hashHex));
        require(!registeredHashes[hashBytes], "Duplicate document hash");
        registeredHashes[hashBytes] = true;

        Document storage doc = registry[bdioId];
        doc.hashHex = hashHex;
        doc.title = title;
        doc.category = category;
        doc.metadataUri = metadataUri;
        doc.owner = msg.sender;
        doc.active = true;
        doc.archived = false;
        doc.timestamp = block.timestamp;

        doc.versions.push(Version({
            hashHex: hashHex,
            timestamp: block.timestamp,
            note: "Initial",
            editor: msg.sender,
            txHash: ""
        }));

        doc.history.push(OwnershipHistory({
            owner: msg.sender,
            timestamp: block.timestamp,
            note: "Initial owner"
        }));

        exists[bdioId] = true;
        lastRegistrationTime[msg.sender] = block.timestamp;

        emit DocumentRegistered(bdioId, msg.sender);
    }

    // Batch register multiple documents
    function batchRegister(
        string[] calldata bdioIds,
        string[] calldata hashHexes,
        string[] calldata titles,
        string[] calldata categories,
        string[] calldata metadataUris,
        uint256[] calldata expiries
    ) external payable 
        nonReentrant 
        whenNotPaused 
        notEmergencyPaused
        rateLimited
    {
        uint count = bdioIds.length;
        require(count > 0 && count <= MAX_BATCH_SIZE, "Invalid batch size");
        require(
            hashHexes.length == count &&
            titles.length == count &&
            categories.length == count &&
            metadataUris.length == count &&
            expiries.length == count,
            "Mismatched array lengths"
        );
        
        // Safe multiplication to prevent overflow
        uint256 totalFee = registerFee * count;
        require(totalFee / count == registerFee, "Overflow detected");
        require(msg.value >= totalFee, "Insufficient batch register fee");

        for (uint i = 0; i < count; i++) {
            _validateInputs(bdioIds[i], hashHexes[i], titles[i], categories[i], metadataUris[i]);
            
            require(!exists[bdioIds[i]], "Already exists");

            bytes32 hashBytes = keccak256(bytes(hashHexes[i]));
            require(!registeredHashes[hashBytes], "Duplicate document hash");
            registeredHashes[hashBytes] = true;

            Document storage doc = registry[bdioIds[i]];
            doc.hashHex = hashHexes[i];
            doc.title = titles[i];
            doc.category = categories[i];
            doc.metadataUri = metadataUris[i];
            doc.owner = msg.sender;
            doc.active = true;
            doc.archived = false;
            doc.timestamp = block.timestamp;

            doc.versions.push(Version({
                hashHex: hashHexes[i],
                timestamp: block.timestamp,
                note: "Initial",
                editor: msg.sender,
                txHash: ""
            }));

            doc.history.push(OwnershipHistory({
                owner: msg.sender,
                timestamp: block.timestamp,
                note: "Initial owner"
            }));

            documentExpiry[bdioIds[i]] = expiries[i];
            exists[bdioIds[i]] = true;

            emit DocumentRegistered(bdioIds[i], msg.sender);
        }

        lastRegistrationTime[msg.sender] = block.timestamp;
        emit DocumentBatchRegistered(count);
    }

    // Internal validation function
    function _validateInputs(
        string calldata bdioId,
        string calldata hashHex,
        string calldata title,
        string calldata category,
        string calldata metadataUri
    ) internal pure {
        require(bytes(bdioId).length >= MIN_BDIO_ID_LENGTH && bytes(bdioId).length <= MAX_BDIO_ID_LENGTH, "Invalid BDIO ID");
        require(bytes(hashHex).length == HASH_LENGTH, "Invalid hash length");
        require(bytes(title).length <= MAX_STRING_LENGTH, "Title too long");
        require(bytes(category).length <= MAX_STRING_LENGTH, "Category too long");
        require(bytes(metadataUri).length <= MAX_STRING_LENGTH, "MetadataUri too long");
        require(_isValidHex(hashHex), "Invalid hex format");
    }

    // Mint NFT
    function mintNFT(string calldata bdioId) external payable
        onlyDocOwner(bdioId)
        nonReentrant
        whenNotPaused
        notEmergencyPaused
    {
        require(msg.value >= mintFee, "Insufficient mint fee");
        require(bdioToTokenId[bdioId] == 0, "Already minted");

        uint256 newTokenId = nextTokenId.add(1);
        require(bytes(tokenIdToBdio[newTokenId]).length == 0, "tokenId already used");

        bdioToTokenId[bdioId] = newTokenId;
        tokenIdToBdio[newTokenId] = bdioId;

        nextTokenId = newTokenId;
        _safeMint(msg.sender, newTokenId);
    }

    // Burn NFT
    function burnNFT(string calldata bdioId) external
        onlyDocOwner(bdioId)
        nonReentrant
        whenNotPaused
        notEmergencyPaused
    {
        uint256 tokenId = bdioToTokenId[bdioId];
        require(tokenId != 0, "Not minted");
        _burn(tokenId);
        bdioToTokenId[bdioId] = 0;
        delete tokenIdToBdio[tokenId];
    }

    // Add new version (with global hash uniqueness)
    function addVersion(
        string calldata bdioId,
        string calldata newHash,
        string calldata note,
        string calldata txHash
    ) external payable 
        onlyDocOwner(bdioId) 
        validHash(newHash)
        validString(note)
        validString(txHash)
        nonReentrant
        whenNotPaused
        notEmergencyPaused
    {
        require(exists[bdioId], "Not found");
        require(msg.value >= versionFee, "Insufficient version fee");

        bytes32 hashBytes = keccak256(bytes(newHash));
        require(!registeredHashes[hashBytes], "Duplicate document hash");
        registeredHashes[hashBytes] = true;

        registry[bdioId].versions.push(Version({
            hashHex: newHash,
            timestamp: block.timestamp,
            note: note,
            editor: msg.sender,
            txHash: txHash
        }));

        emit VersionAdded(bdioId, newHash, note, msg.sender);
    }

    // Archive & deactivate
    function archiveDocument(string calldata bdioId, string calldata reason) external 
        onlyDocOwner(bdioId) 
        validString(reason)
        whenNotPaused
    {
        require(!registry[bdioId].archived, "Already archived");
        registry[bdioId].archived = true;
        emit DocumentArchived(bdioId, reason, block.timestamp);
    }

    function deactivateDocument(string calldata bdioId, string calldata reason) external 
        onlyDocOwner(bdioId) 
        validString(reason)
        whenNotPaused
    {
        require(registry[bdioId].active, "Already inactive");
        registry[bdioId].active = false;
        emit DocumentDeactivated(bdioId, reason, block.timestamp);
    }

    // Transfer manual ownership
    function transferOwnershipManual(string calldata bdioId, address newOwner, string calldata note) external 
        onlyDocOwner(bdioId) 
        validAddress(newOwner)
        validString(note)
        whenNotPaused
    {
        address oldOwner = registry[bdioId].owner;
        registry[bdioId].owner = newOwner;

        registry[bdioId].history.push(OwnershipHistory({
            owner: newOwner,
            timestamp: block.timestamp,
            note: note
        }));

        emit OwnershipTransferred(bdioId, oldOwner, newOwner);
    }

    // Update on NFT transfer
    function _afterTokenTransfer(address from, address to, uint256 tokenId, uint256) internal override {
        string memory bdioId = tokenIdToBdio[tokenId];
        require(bytes(bdioId).length != 0, "Unknown tokenId");
        address oldOwner = registry[bdioId].owner;
        registry[bdioId].owner = to;
        registry[bdioId].history.push(OwnershipHistory({
            owner: to,
            timestamp: block.timestamp,
            note: from == address(0) ? "NFT mint" : "NFT transfer"
        }));
        emit OwnershipTransferred(bdioId, oldOwner, to);
    }

    // Utility function to validate hex string
    function _isValidHex(string memory str) internal pure returns (bool) {
        bytes memory b = bytes(str);
        for (uint256 i = 0; i < b.length; i++) {
            bytes1 char = b[i];
            if (!(char >= 0x30 && char <= 0x39) && // 0-9
                !(char >= 0x41 && char <= 0x46) && // A-F
                !(char >= 0x61 && char <= 0x66)) { // a-f
                return false;
            }
        }
        return true;
    }

    // View functions
    function tokenURI(uint256 tokenId) public view override returns (string memory) {
        return string(abi.encodePacked(baseURI, tokenId.toString(), ".json"));
    }

    function getDocumentOwner(string calldata bdioId) external view returns (address) {
        return registry[bdioId].owner;
    }

    function getDocumentVersions(string calldata bdioId) external view returns (Version[] memory) {
        return registry[bdioId].versions;
    }

    function getOwnershipHistory(string calldata bdioId) external view returns (OwnershipHistory[] memory) {
        return registry[bdioId].history;
    }

    function verifyDocument(string calldata bdioId) external view returns (
        address owner, bool active, bool archived, uint256 timestamp, uint versionCount, string memory category, string memory metadataUri
    ) {
        Document storage doc = registry[bdioId];
        return (doc.owner, doc.active, doc.archived, doc.timestamp, doc.versions.length, doc.category, doc.metadataUri);
    }

    function getDocumentByTokenId(uint256 tokenId) external view returns (string memory bdioId, address owner) {
        bdioId = tokenIdToBdio[tokenId];
        require(bytes(bdioId).length != 0, "Not linked");
        owner = registry[bdioId].owner;
    }

    function supportsInterface(bytes4 interfaceId) public view override returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    // Secure withdraw functions
    function withdrawAll() external onlyContractOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "No balance to withdraw");
        
        emit Withdraw(contractOwner, balance);
        
        (bool success, ) = payable(contractOwner).call{value: balance}("");
        require(success, "Transfer failed");
    }

    function withdrawTo(address payable to, uint256 amount) external
        onlyContractOwner
        validAddress(to)
        nonReentrant
    {
        require(address(this).balance >= amount, "Insufficient contract balance");

        emit Withdraw(to, amount);

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");
    }

    /// @notice Initiate ownership transfer (2-step process)
    function transferOwnership(address newOwner) external onlyContractOwner validAddress(newOwner) {
        require(newOwner != contractOwner, "Same owner");
        pendingOwner = newOwner;
        emit OwnershipTransferInitiated(contractOwner, newOwner);
    }

    /// @notice Accept ownership transfer
    function acceptOwnership() external {
        require(msg.sender == pendingOwner, "Not pending owner");
        address oldOwner = contractOwner;
        contractOwner = pendingOwner;
        pendingOwner = address(0);
        emit OwnershipTransferCompleted(oldOwner, contractOwner);
    }

    /// @notice Cancel pending ownership transfer
    function cancelOwnershipTransfer() external onlyContractOwner {
        require(pendingOwner != address(0), "No pending transfer");
        pendingOwner = address(0);
    }
}