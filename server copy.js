const express = require('express');
const path = require('path');
const fs = require("fs");

const app = express();
const PORT = 3000;

// Middleware to parse JSON bodies
app.use(express.json());

// Serve static files from current directory
app.use(express.static(path.join(__dirname, 'public')));
app.use('/abis', express.static(path.join(__dirname, 'abis')));

// Serve register.html at root
// ========== Serve HTML Pages ==========
app.get("/:page", (req, res) => {
  const filePath = path.join(process.cwd(), "public", `${req.params.page}.html`);
  if (fs.existsSync(filePath)) {
    res.sendFile(filePath);
  } else {
    res.status(404).send("Page not found");
  }
});

// POST endpoint to save metadata JSON file
app.post('/api/saveMetadata', (req, res) => {
  const metadata = req.body;
  if (!metadata || !metadata.bdioId) {
    return res.status(400).json({ error: 'Invalid metadata: missing bdioId' });
  }

  const metadataDir = path.join(__dirname, 'public', 'metadata');
  if (!fs.existsSync(metadataDir)) {
    fs.mkdirSync(metadataDir, { recursive: true });
  }

  const filePath = path.join(metadataDir, `${metadata.bdioId}.meta.json`);
  fs.writeFile(filePath, JSON.stringify(metadata, null, 2), (err) => {
    if (err) {
      console.error('Error writing metadata file:', err);
      return res.status(500).json({ error: 'Failed to save metadata file' });
    }
    res.json({ message: 'Metadata saved successfully' });
  });
});

app.listen(PORT, () => {
  console.log(`Server is running at http://localhost:${PORT}`);
});
