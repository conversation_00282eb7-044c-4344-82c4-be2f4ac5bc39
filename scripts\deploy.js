const hre = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("🚀 Starting deployment...");
  console.log("🌐 Network:", hre.network.name);
  console.log("⛽ Chain ID:", hre.network.config.chainId);

  const [deployer] = await hre.ethers.getSigners();
  console.log("👤 Deploying from address:", deployer.address);

  // Check balance
  const balance = await hre.ethers.provider.getBalance(deployer.address);
  console.log("💰 Deployer balance:", hre.ethers.formatEther(balance), "MATIC");

  // Ensure we have enough balance for deployment
  if (balance < hre.ethers.parseEther("0.1")) {
    console.warn("⚠️ Warning: Low balance. Make sure you have enough MATIC for gas fees.");
  }

  // 1️⃣ Deploy BDIOCoreRegistry
  console.log("\n📋 Deploying BDIOCoreRegistry...");
  const BDIOCoreRegistry = await hre.ethers.getContractFactory("BDIOCoreRegistry");
  const bdioCoreRegistry = await BDIOCoreRegistry.deploy();
  await bdioCoreRegistry.waitForDeployment();
  const bdioCoreRegistryAddress = await bdioCoreRegistry.getAddress();
  console.log(`✅ BDIOCoreRegistry deployed at: ${bdioCoreRegistryAddress}`);

  // Wait for block confirmations on mainnet
  if (hre.network.name === "polygon") {
    console.log("⏳ Waiting for block confirmations...");
    await bdioCoreRegistry.deploymentTransaction().wait(5);
  }

  // 2️⃣ Deploy AccessControlManager with dummy endorsementManager
  console.log("\n🔐 Deploying AccessControlManager...");
  const dummyEndorsementManager = "******************************************";
  const AccessControlManager = await hre.ethers.getContractFactory("AccessControlManager");
  const accessControlManager = await AccessControlManager.deploy(
    bdioCoreRegistryAddress,
    dummyEndorsementManager
  );
  await accessControlManager.waitForDeployment();
  const accessControlManagerAddress = await accessControlManager.getAddress();
  console.log(`✅ AccessControlManager deployed at: ${accessControlManagerAddress}`);

  // Wait for block confirmations on mainnet
  if (hre.network.name === "polygon") {
    console.log("⏳ Waiting for block confirmations...");
    await accessControlManager.deploymentTransaction().wait(5);
  }

  // 3️⃣ Deploy EndorsementManager with real accessControlManager + bdioCoreRegistry
  console.log("\n📝 Deploying EndorsementManager...");
  const EndorsementManager = await hre.ethers.getContractFactory("EndorsementManager");
  const endorsementManager = await EndorsementManager.deploy(
    accessControlManagerAddress,
    bdioCoreRegistryAddress
  );
  await endorsementManager.waitForDeployment();
  const endorsementManagerAddress = await endorsementManager.getAddress();
  console.log(`✅ EndorsementManager deployed at: ${endorsementManagerAddress}`);

  // Wait for block confirmations on mainnet
  if (hre.network.name === "polygon") {
    console.log("⏳ Waiting for block confirmations...");
    await endorsementManager.deploymentTransaction().wait(5);
  }

  // 4️⃣ Call setter to update endorsementManager in AccessControlManager
  console.log("\n🔄 Updating AccessControlManager with real EndorsementManager address...");
  const accessControlManagerContract = AccessControlManager.attach(accessControlManagerAddress);
  const tx = await accessControlManagerContract.setEndorsementManager(endorsementManagerAddress);
  await tx.wait();
  console.log(`✅ AccessControlManager updated with real EndorsementManager address`);

  // 5️⃣ Deploy BDIOExpiryManager
  console.log("\n⏰ Deploying BDIOExpiryManager...");
  const BDIOExpiryManager = await hre.ethers.getContractFactory("BDIOExpiryManager");
  const bdioExpiryManager = await BDIOExpiryManager.deploy(bdioCoreRegistryAddress);
  await bdioExpiryManager.waitForDeployment();
  const bdioExpiryManagerAddress = await bdioExpiryManager.getAddress();
  console.log(`✅ BDIOExpiryManager deployed at: ${bdioExpiryManagerAddress}`);

  // Wait for block confirmations on mainnet
  if (hre.network.name === "polygon") {
    console.log("⏳ Waiting for block confirmations...");
    await bdioExpiryManager.deploymentTransaction().wait(5);
  }

  // 6️⃣ Deploy VCManager
  console.log("\n📄 Deploying VCManager...");
  const VCManager = await hre.ethers.getContractFactory("VCManager");
  const vcManager = await VCManager.deploy(bdioCoreRegistryAddress);
  await vcManager.waitForDeployment();
  const vcManagerAddress = await vcManager.getAddress();
  console.log(`✅ VCManager deployed at: ${vcManagerAddress}`);

  // Wait for block confirmations on mainnet
  if (hre.network.name === "polygon") {
    console.log("⏳ Waiting for block confirmations...");
    await vcManager.deploymentTransaction().wait(5);
  }

  // ✏️ Save deployments
  console.log("\n💾 Saving deployment information...");
  const deployments = {
    BDIOCoreRegistry: bdioCoreRegistryAddress,
    AccessControlManager: accessControlManagerAddress,
    EndorsementManager: endorsementManagerAddress,
    BDIOExpiryManager: bdioExpiryManagerAddress,
    VCManager: vcManagerAddress,
    network: hre.network.name,
    chainId: hre.network.config.chainId,
    deployer: deployer.address,
    deployedAt: new Date().toISOString(),
    gasUsed: {
      // You can add gas tracking here if needed
    }
  };

  const filePath = path.join(__dirname, "..", "deployments.json");
  fs.writeFileSync(filePath, JSON.stringify(deployments, null, 2));

  console.log("\n📦 deployments.json saved:");
  console.log(JSON.stringify(deployments, null, 2));

  console.log("\n🎉 All contracts deployed successfully!");
  console.log("\n📋 Next steps:");
  console.log("1. Run verification: npx hardhat run scripts/verify.js --network polygon");
  console.log("2. Update your .env file with the new contract addresses");
  console.log("3. Test the contracts on Polygon mainnet");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    console.error(error.stack);
    process.exit(1);
  });
