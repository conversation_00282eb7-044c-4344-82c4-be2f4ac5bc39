const hre = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Starting deployment...");

  const [deployer] = await hre.ethers.getSigners();
  console.log("👤 Deploying from address:", deployer.address);

  // 1️⃣ Deploy BDIOCoreRegistry
  const BDIOCoreRegistry = await hre.ethers.getContractFactory("BDIOCoreRegistry");
  const bdioCoreRegistry = await BDIOCoreRegistry.deploy();
  await bdioCoreRegistry.waitForDeployment();
  const bdioCoreRegistryAddress = await bdioCoreRegistry.getAddress();
  console.log(`✅ BDIOCoreRegistry deployed at: ${bdioCoreRegistryAddress}`);

  // 2️⃣ Deploy AccessControlManager with dummy endorsementManager
  const dummyEndorsementManager = "******************************************";
  const AccessControlManager = await hre.ethers.getContractFactory("AccessControlManager");
  const accessControlManager = await AccessControlManager.deploy(
    bdioCoreRegistryAddress,
    dummyEndorsementManager
  );
  await accessControlManager.waitForDeployment();
  const accessControlManagerAddress = await accessControlManager.getAddress();
  console.log(`✅ AccessControlManager deployed at: ${accessControlManagerAddress}`);

  // 3️⃣ Deploy EndorsementManager with real accessControlManager + bdioCoreRegistry
  const EndorsementManager = await hre.ethers.getContractFactory("EndorsementManager");
  const endorsementManager = await EndorsementManager.deploy(
    accessControlManagerAddress,
    bdioCoreRegistryAddress
  );
  await endorsementManager.waitForDeployment();
  const endorsementManagerAddress = await endorsementManager.getAddress();
  console.log(`✅ EndorsementManager deployed at: ${endorsementManagerAddress}`);

  // 4️⃣ Call setter to update endorsementManager in AccessControlManager
  const accessControlManagerContract = AccessControlManager.attach(accessControlManagerAddress);
  const tx = await accessControlManagerContract.setEndorsementManager(endorsementManagerAddress);
  await tx.wait();
  console.log(`✅ AccessControlManager updated with real EndorsementManager address`);

  // 5️⃣ Deploy BDIOExpiryManager
  const BDIOExpiryManager = await hre.ethers.getContractFactory("BDIOExpiryManager");
  const bdioExpiryManager = await BDIOExpiryManager.deploy(bdioCoreRegistryAddress);
  await bdioExpiryManager.waitForDeployment();
  const bdioExpiryManagerAddress = await bdioExpiryManager.getAddress();
  console.log(`✅ BDIOExpiryManager deployed at: ${bdioExpiryManagerAddress}`);

  // 6️⃣ Deploy VCManager
  const VCManager = await hre.ethers.getContractFactory("VCManager");
  const vcManager = await VCManager.deploy(bdioCoreRegistryAddress);
  await vcManager.waitForDeployment();
  const vcManagerAddress = await vcManager.getAddress();
  console.log(`✅ VCManager deployed at: ${vcManagerAddress}`);

  // ✏️ Save deployments
  const deployments = {
    BDIOCoreRegistry: bdioCoreRegistryAddress,
    AccessControlManager: accessControlManagerAddress,
    EndorsementManager: endorsementManagerAddress,
    BDIOExpiryManager: bdioExpiryManagerAddress,
    VCManager: vcManagerAddress,
    network: hre.network.name,
    updatedAt: new Date().toISOString()
  };

  const filePath = path.join(__dirname, "..", "deployments.json");
  fs.writeFileSync(filePath, JSON.stringify(deployments, null, 2));

  console.log("\n📦 deployments.json saved:");
  console.log(JSON.stringify(deployments, null, 2));

  console.log("\n🎉 All contracts deployed successfully!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    console.error(error.stack);
    process.exit(1);
  });
