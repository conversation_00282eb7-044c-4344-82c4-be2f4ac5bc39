{"_format": "hh-sol-artifact-1", "contractName": "IEndorsementManager", "sourceName": "contracts/AccessControlManager.sol", "abi": [{"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getEndorsementByIndex", "outputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "string", "name": "note", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "bdioId", "type": "string"}], "name": "getEndorsementsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}