const hre = require("hardhat");

async function main() {
  const contractAddress = "0x2E40a0A7FF3FECD76555A9BEc2c56BF1BBA25755"; // Updated with actual deployed address

  try {
    await hre.run("verify:verify", {
      address: contractAddress,
      constructorArguments: [
        "0x5416014af135e6b6593a78c473b24fdc0ba69b91",
        "0x1Afe84BC926E9Fe0e2fE4cbb1B54742CcC87E035"
      ], // Updated with actual constructor arguments
    });
    console.log("Contract verified successfully");
  } catch (error) {
    console.error("Verification failed:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
