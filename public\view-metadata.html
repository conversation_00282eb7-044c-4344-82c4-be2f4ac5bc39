<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>📦 BDIO Metadata Viewer</title>
<style>
  body { font-family: Arial, sans-serif; max-width: 800px; margin: 2em auto; padding: 1em; background: #f9fafb; color: #333; }
  h2 { color: #1e40af; }
  table { width: 100%; border-collapse: collapse; margin-bottom: 1em; }
  th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
  th { background: #1e40af; color: white; }
  tr:nth-child(even) { background: #f1f5f9; }
  code { background: #eee; padding: 2px 4px; border-radius: 3px; }
</style>
</head>
<body>

<h2>📄 BDIO Metadata Viewer</h2>

<label>Metadata URL:
  <input type="text" id="metaUrl" value="https://localhost/metadata/3a83259ef9.meta.json" style="width:100%;"/>
</label>
<button onclick="loadMetadata()">Load Metadata</button>

<div id="content"></div>

<script>
async function loadMetadata() {
  const url = document.getElementById('metaUrl').value.trim();
  if (!url) return alert('Please enter metadata URL');
  document.getElementById('content').innerHTML = 'Loading...';

  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error('Failed to fetch: ' + res.status);
    const data = await res.json();

    let html = '';

    html += `<h3>📝 Basic Info</h3>`;
    html += `<table>
      <tr><th>BDIO ID</th><td>${data.bdioId}</td></tr>
      <tr><th>Title</th><td>${data.title}</td></tr>
      <tr><th>Category</th><td>${(data.category||[]).join(', ')}</td></tr>
      <tr><th>HashHex</th><td><code>${data.hashHex}</code></td></tr>
      <tr><th>Blockchain Tx</th><td>${data.blockchaintxHash ? `<a href="https://polygonscan.com/tx/${data.blockchaintxHash}" target="_blank">${data.blockchaintxHash}</a>` : 'N/A'}</td></tr>
      <tr><th>Issuer</th><td>${data.issuer}</td></tr>
      <tr><th>Timestamp</th><td>${data.timestamp}</td></tr>
      <tr><th>Expiry</th><td>${data.expiry || 'None'}</td></tr>
      <tr><th>Image</th><td><img src="${data.image}" alt="Image" style="max-width:150px;"></td></tr>
      <tr><th>metaUri</th><td><a href="${data.metaUri}" target="_blank">${data.metaUri}</a></td></tr>
    </table>`;

    // Versions
    if (data.versions && data.versions.length > 0) {
      html += `<h3>🗂 Versions</h3>
        <table>
        <tr><th>Hash</th><th>Note</th><th>Editor</th><th>Timestamp</th></tr>`;
      data.versions.forEach(v => {
        html += `<tr>
          <td><code>${v[0]}</code></td>
          <td>${v[2]}</td>
          <td>${v[3]}</td>
          <td>${v.timestamp}</td>
        </tr>`;
      });
      html += '</table>';
    }

    // Ownership history
    if (data.ownershipHistory && data.ownershipHistory.length > 0) {
      html += `<h3>👑 Ownership History</h3>
        <table>
        <tr><th>Owner</th><th>Note</th><th>Timestamp</th></tr>`;
      data.ownershipHistory.forEach(o => {
        html += `<tr>
          <td>${o[0]}</td>
          <td>${o[2]}</td>
          <td>${o.timestamp}</td>
        </tr>`;
      });
      html += '</table>';
    }

    // Endorsements
    if (data.endorsements && data.endorsements.length > 0) {
      html += `<h3>✍️ Endorsements</h3>
        <table>
        <tr><th>Signer</th><th>Note</th><th>Timestamp</th></tr>`;
      data.endorsements.forEach(e => {
        html += `<tr>
          <td>${e.signer}</td>
          <td>${e.note}</td>
          <td>${e.timestamp}</td>
        </tr>`;
      });
      html += '</table>';
    } else {
      html += '<p>✅ No endorsements yet.</p>';
    }

    // NFT info
    if (data.nft) {
      html += `<h3>🎨 NFT Info</h3>
        <p>Token ID: <code>${data.nft.tokenId}</code></p>
        <p>Contract: ${data.nft.contract}</p>`;
    }

    // VC info
    if (data.vc) {
      html += `<h3>🔒 VC Info</h3>
        <p>VC Hash: <code>${data.vc.hash}</code></p>`;
    }

    document.getElementById('content').innerHTML = html;

  } catch(e) {
    console.error(e);
    document.getElementById('content').innerHTML = '❌ Error: ' + (e.message || e);
  }
}
</script>

</body>
</html>
