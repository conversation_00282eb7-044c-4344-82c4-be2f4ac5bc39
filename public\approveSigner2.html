<!DOCTYPE html>
<html>
<head>
  <title>Approve Signer</title>
  <script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
</head>
<body>
  <h2>Approve Signer</h2>
  <form id="approveForm">
    <label>BDIO ID:</label><br>
    <input type="text" id="bdioId" value="test-123"><br>
    <label>Signer Address:</label><br>
    <input type="text" id="signerAddress"><br><br>
    <button type="submit">Approve Signer</button>
  </form>

  <p id="status"></p>

  <script>
    const contractAddress = '******************************************'; // ganti dengan alamat contract AccessControlManager 
    const abi = [ 
      "function approveSigner(string bdioId, address signer) public",
    ];

    document.getElementById('approveForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      const bdioId = document.getElementById('bdioId').value;
      const signerAddress = document.getElementById('signerAddress').value;

      try {
        // 1. Koneksi ke Metamask
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send("eth_requestAccounts", []);
        const signer = await provider.getSigner();

        // 2. Buat instance contract
        const contract = new ethers.Contract(contractAddress, abi, signer);

        // 3. Panggil approveSigner
        const tx = await contract.approveSigner(bdioId, signerAddress);
        document.getElementById('status').textContent = "Transaction sent. Waiting confirmation...";

        await tx.wait(); // tunggu sampai transaksi confirm
        document.getElementById('status').textContent = "Signer approved successfully!";
        console.log("Tx hash:", tx.hash);
      } catch (err) {
        console.error(err);
        document.getElementById('status').textContent = "Error: " + (err.reason || err.message);
      }
    });
  </script>
</body>
</html>
