// scripts/deploy.js
const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Starting deploy...");

  const [deployer] = await hre.ethers.getSigners();
  console.log("Deploying from:", deployer.address);

  const deployments = {};

  // BDIOCoreRegistry
  const BDIOCoreRegistry = await hre.ethers.getContractFactory("BDIOCoreRegistry");
  const bdio = await BDIOCoreRegistry.deploy();
  await bdio.deployed();
  console.log("✅ BDIOCoreRegistry deployed:", bdio.address);
  deployments.BDIOCoreRegistry = bdio.address;

  // AccessControlManager
  const AccessControlManager = await hre.ethers.getContractFactory("AccessControlManager");
  const acm = await AccessControlManager.deploy();
  await acm.deployed();
  console.log("✅ AccessControlManager deployed:", acm.address);
  deployments.AccessControlManager = acm.address;

  // EndorsementManager
  const EndorsementManager = await hre.ethers.getContractFactory("EndorsementManager");
  const endorse = await EndorsementManager.deploy(acm.address);
  await endorse.deployed();
  console.log("✅ EndorsementManager deployed:", endorse.address);
  deployments.EndorsementManager = endorse.address;

  // CategoryManager
  const CategoryManager = await hre.ethers.getContractFactory("CategoryManager");
  const cat = await CategoryManager.deploy();
  await cat.deployed();
  console.log("✅ CategoryManager deployed:", cat.address);
  deployments.CategoryManager = cat.address;

  // MetadataManager
  const MetadataManager = await hre.ethers.getContractFactory("MetadataManager");
  const meta = await MetadataManager.deploy();
  await meta.deployed();
  console.log("✅ MetadataManager deployed:", meta.address);
  deployments.MetadataManager = meta.address;

  // VCManager
  const VCManager = await hre.ethers.getContractFactory("VCManager");
  const vc = await VCManager.deploy();
  await vc.deployed();
  console.log("✅ VCManager deployed:", vc.address);
  deployments.VCManager = vc.address;

  // NFTWrapper
  const NFTWrapper = await hre.ethers.getContractFactory("NFTWrapper");
  const nft = await NFTWrapper.deploy();
  await nft.deployed();
  console.log("✅ NFTWrapper deployed:", nft.address);
  deployments.NFTWrapper = nft.address;

  console.log("🎉 All contracts deployed!");
  console.log(deployments);

  fs.writeFileSync("deployments.json", JSON.stringify(deployments, null, 2));
}

main().catch((error) => {
  console.error("❌ Deploy script failed:", error);
  process.exit(1);
});