/* General body styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f9f9f9;
  color: #333;
  margin: 0;
  padding: 20px;
}

/* Container for the content */
h1 {
  text-align: center;
  color: #222;
  margin-bottom: 20px;
}

input#bdioIdInput {
  width: 300px;
  padding: 10px;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 10px;
  box-sizing: border-box;
}

button {
  padding: 10px 20px;
  font-size: 1rem;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #0056b3;
}

#documentInfo {
  max-width: 800px;
  margin: 30px auto 0 auto;
  background-color: white;
  padding: 20px 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#documentInfo h2, #documentInfo h3 {
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
  margin-top: 20px;
  color: #007bff;
}

#documentInfo p {
  font-size: 1rem;
  margin: 8px 0;
}

#documentInfo p strong {
  width: 140px;
  display: inline-block;
  color: #555;
}

ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 10px;
}

ul li {
  margin-bottom: 6px;
  font-size: 0.95rem;
  color: #444;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  input#bdioIdInput {
    width: 100%;
    margin-bottom: 10px;
  }

  button {
    width: 100%;
  }

  #documentInfo {
    padding: 15px 20px;
  }
}

/* Loading spinner styles */
#loadingSpinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 123, 255, 0.3);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s ease-in-out infinite;
  vertical-align: middle;
  margin-left: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
