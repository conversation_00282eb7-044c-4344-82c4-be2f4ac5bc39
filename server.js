// SPDX-License-Identifier: MIT
const express = require('express');
const path = require('path');
const fs = require('fs');
const { ethers } = require('ethers');
const dotenv = require('dotenv');
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// ✅ Provider & contract (simplified to use only BDIOCoreRegistry)
const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'https://polygon-rpc.com/');
const BDIOCoreABI = require('./abis/BDIOCoreRegistry.json');

// Main contract instance
const bdio = new ethers.Contract(
  process.env.BDIO_CORE_ADDRESS || 'YOUR_CONTRACT_ADDRESS_HERE',
  BDIOCoreABI.abi,
  provider
);

// Contract configuration
const CONTRACT_CONFIG = {
  address: process.env.BDIO_CORE_ADDRESS || 'YOUR_CONTRACT_ADDRESS_HERE',
  network: process.env.NETWORK || 'polygon',
  baseUrl: process.env.BASE_URL || 'http://localhost:3000'
};

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));
app.use('/metadata', express.static(path.join(__dirname, 'public', 'metadata')));
app.use('/abis', express.static(path.join(__dirname, 'abis')));

// ========== Serve HTML Pages ==========
app.get("/:page", (req, res) => {
  const filePath = path.join(process.cwd(), "public", `${req.params.page}.html`);
  fs.existsSync(filePath) ? res.sendFile(filePath) : res.status(404).send("Page not found");
});

const metadataDir = path.join(process.cwd(), 'public', 'metadata');
if (!fs.existsSync(metadataDir)) fs.mkdirSync(metadataDir, { recursive: true });

// ========= Helper: retry with delay =========
const retry = async (fn, retries = 3, delay = 1500) => {
  for (let i = 0; i < retries; i++) {
    try { return await fn(); }
    catch (e) { if (i === retries - 1) throw e; await new Promise(r => setTimeout(r, delay)); }
  }
};

// ========== saveMetadata ==========
app.post('/api/saveMetadata', async (req, res) => {
  try {
    const { bdioId, title, category, hashHex, blockchaintxHash, issuer } = req.body;
    if (!bdioId) return res.status(400).json({ error: 'Missing bdioId' });

    console.log(`📝 Saving metadata for BDIO ID: ${bdioId}`);

    // Check if document exists in the contract
    const exists = await bdio.exists(bdioId);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found in contract' });
    }

    // Get smart contract data
    const [owner, active, archived, ts, versionCount, categoryOnChain] = await bdio.verifyDocument(bdioId);
    const versionsRaw = await retry(() => bdio.getDocumentVersions(bdioId));
    const ownership = await retry(() => bdio.getOwnershipHistory(bdioId));

    // Get NFT token ID (if minted)
    let tokenId = 0;
    try {
      const tokenIdRaw = await bdio.bdioToTokenId(bdioId);
      tokenId = Number(tokenIdRaw);
    } catch (e) {
      console.warn(`Could not get token ID for ${bdioId}:`, e.message);
    }

    // Get document expiry (if available in the new contract)
    let expiryTime = 0;
    try {
      // Check if the contract has documentExpiry mapping
      expiryTime = await bdio.documentExpiry(bdioId);
    } catch (e) {
      // documentExpiry might not be available in all contract versions
      console.warn(`Could not get expiry for ${bdioId}:`, e.message);
    }

    // Format versions data
    const versions = versionsRaw.map(v => ({
      hashHex: v.hashHex,
      timestamp: new Date(Number(v.timestamp) * 1000).toISOString(),
      note: v.note,
      editor: v.editor,
      txHash: v.txHash || ''
    }));

    // Format ownership history
    const ownershipHistory = ownership.map(o => ({
      owner: o.owner,
      timestamp: new Date(Number(o.timestamp) * 1000).toISOString(),
      note: o.note
    }));

    // Create final metadata object
    const finalMetadata = {
      bdioId,
      title: title || categoryOnChain || `Document ${bdioId.substring(0, 8)}...`,
      category: category ? [category] : [categoryOnChain || 'default'],
      hashHex: hashHex || (versions.length > 0 ? versions[0].hashHex : ''),
      blockchaintxHash: blockchaintxHash || '',
      issuer: issuer || owner,
      timestamp: new Date().toISOString(),
      expiry: expiryTime > 0 ? new Date(Number(expiryTime) * 1000).toISOString() : null,
      versions,
      signers: [], // For future endorsement system integration
      ownershipHistory,
      endorsements: [], // For future endorsement system integration
      nft: tokenId > 0 ? {
        tokenId: tokenId,
        contract: CONTRACT_CONFIG.address,
        tokenUri: `${CONTRACT_CONFIG.baseUrl}/metadata/${tokenId}.json`
      } : null,
      vc: null, // For future VC system integration
      image: `${CONTRACT_CONFIG.baseUrl}/assets/doc.png`,
      metaUri: `${CONTRACT_CONFIG.baseUrl}/metadata/${bdioId}.meta.json`,
      // Additional metadata
      contractInfo: {
        address: CONTRACT_CONFIG.address,
        network: CONTRACT_CONFIG.network,
        active,
        archived,
        createdAt: new Date(Number(ts) * 1000).toISOString(),
        versionCount: Number(versionCount)
      }
    };

    // Helper replacer to convert BigInt to string
    const jsonBigIntReplacer = (_, value) => typeof value === 'bigint' ? value.toString() : value;

    // Save metadata file
    const filePath = path.join(metadataDir, `${bdioId}.meta.json`);
    fs.writeFileSync(filePath, JSON.stringify(finalMetadata, jsonBigIntReplacer, 2), 'utf-8');

    console.log(`✅ Saved metadata for ${bdioId} (${versions.length} versions, NFT: ${tokenId > 0 ? tokenId : 'none'})`);
    res.json({
      message: 'Metadata saved successfully',
      bdioId,
      filePath: `${bdioId}.meta.json`,
      hasNFT: tokenId > 0,
      tokenId: tokenId > 0 ? tokenId : null
    });

  } catch (e) {
    console.error('❌ Error saving metadata:', e);
    res.status(500).json({
      error: e.message || 'Failed to save metadata',
      details: e.stack
    });
  }
});

// ========== updateMetadata ==========
app.post('/api/updateMetadata', async (req, res) => {
  try {
    const { bdioId } = req.body;
    if (!bdioId) return res.status(400).json({ error: 'Missing bdioId' });

    console.log(`🔄 Updating metadata for BDIO ID: ${bdioId}`);

    // Check if document exists
    const exists = await bdio.exists(bdioId);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found in contract' });
    }

    const filePath = path.join(metadataDir, `${bdioId}.meta.json`);
    let existing = fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath,'utf-8')) : {};

    // Get updated contract data
    const [, active, archived, ts, , categoryOnChain] = await bdio.verifyDocument(bdioId);

    // Allow updates even for inactive/archived documents for historical purposes
    // if (!active || archived) return res.status(400).json({ error:'Document inactive or archived' });

    const versionsRaw = await bdio.getDocumentVersions(bdioId);
    const ownership = await bdio.getOwnershipHistory(bdioId);

    // Get NFT token ID (if minted)
    let tokenId = 0;
    try {
      const tokenIdRaw = await bdio.bdioToTokenId(bdioId);
      tokenId = Number(tokenIdRaw);
    } catch (e) {
      console.warn(`Could not get token ID for ${bdioId}:`, e.message);
    }

    // Get document expiry (if available)
    let expiryTime = 0;
    try {
      expiryTime = await bdio.documentExpiry(bdioId);
    } catch (e) {
      console.warn(`Could not get expiry for ${bdioId}:`, e.message);
    }

    // Format versions data
    const versions = versionsRaw.map(v => ({
      hashHex: v.hashHex,
      timestamp: new Date(Number(v.timestamp) * 1000).toISOString(),
      note: v.note,
      editor: v.editor,
      txHash: v.txHash || ''
    }));

    // Format ownership history
    const ownershipHistory = ownership.map(o => ({
      owner: o.owner,
      timestamp: new Date(Number(o.timestamp) * 1000).toISOString(),
      note: o.note
    }));

    // Update metadata with new contract data
    const updated = {
      ...existing,
      // Update timestamp
      lastUpdated: new Date().toISOString(),
      // Update contract data
      expiry: expiryTime > 0 ? new Date(Number(expiryTime) * 1000).toISOString() : null,
      versions,
      ownershipHistory,
      endorsements: existing.endorsements || [], // Keep existing endorsements for now
      signers: existing.signers || [], // Keep existing signers for now
      nft: tokenId > 0 ? {
        tokenId: tokenId,
        contract: CONTRACT_CONFIG.address,
        tokenUri: `${CONTRACT_CONFIG.baseUrl}/metadata/${tokenId}.json`
      } : null,
      vc: existing.vc || null, // Keep existing VC data
      // Update contract info
      contractInfo: {
        address: CONTRACT_CONFIG.address,
        network: CONTRACT_CONFIG.network,
        active,
        archived,
        createdAt: new Date(Number(ts) * 1000).toISOString(),
        versionCount: versions.length
      }
    };

    // Helper replacer to convert BigInt to string
    const jsonBigIntReplacer = (_, value) => typeof value === 'bigint' ? value.toString() : value;

    // Save updated metadata
    fs.writeFileSync(filePath, JSON.stringify(updated, jsonBigIntReplacer, 2), 'utf-8');
    console.log(`✅ Updated metadata for ${bdioId} (${versions.length} versions)`);

    // ✅ Auto generate {tokenId}.json if missing and NFT exists
    if (tokenId > 0) {
      const nftFile = path.join(metadataDir, `${tokenId}.json`);
      if (!fs.existsSync(nftFile)) {
        console.log(`ℹ️ NFT metadata for tokenId ${tokenId} missing. Generating...`);
        const nftData = {
          bdioId,
          tokenId: tokenId,
          contract: CONTRACT_CONFIG.address,
          name: existing.title || `BDIO Document #${bdioId}`,
          description: existing.description || 'Blockchain Document Identity Object (BDIO) NFT - Immutable proof of document authenticity and ownership.',
          image: existing.image || `${CONTRACT_CONFIG.baseUrl}/assets/nft.png`,
          external_url: `${CONTRACT_CONFIG.baseUrl}/get?bdioId=${bdioId}`,
          attributes: [
            { trait_type: 'BDIO ID', value: bdioId },
            { trait_type: 'Category', value: categoryOnChain || 'Document' },
            { trait_type: 'Created At', value: new Date(Number(ts) * 1000).toISOString() },
            { trait_type: 'Version Count', value: versions.length },
            { trait_type: 'Network', value: CONTRACT_CONFIG.network },
            { trait_type: 'Contract', value: CONTRACT_CONFIG.address }
          ]
        };
        fs.writeFileSync(nftFile, JSON.stringify(nftData, null, 2), 'utf-8');
        console.log(`✅ NFT metadata file created: ${tokenId}.json`);
      }
    }

    res.json({
      message: 'Metadata updated successfully',
      bdioId,
      versionsCount: versions.length,
      hasNFT: tokenId > 0,
      tokenId: tokenId > 0 ? tokenId : null,
      active,
      archived
    });

  } catch (e) {
    console.error('❌ Error updating metadata:', e);
    res.status(500).json({
      error: e.message || 'Failed to update metadata',
      details: e.stack
    });
  }
});

// ========== saveNft ==========
app.post('/api/saveNft', async (req, res) => {
  try {
    const { bdioId, tokenId, contract, name, description, image, external_url, attributes } = req.body;
    if (!bdioId) return res.status(400).json({ error: 'Missing bdioId' });

    console.log(`🎨 Saving NFT metadata for BDIO ID: ${bdioId}`);

    // Check if document exists
    const exists = await bdio.exists(bdioId);
    if (!exists) {
      return res.status(404).json({ error: 'Document not found in contract' });
    }

    // Get token ID from contract or use provided one
    let finalTokenId = tokenId;
    if (!finalTokenId) {
      try {
        const tokenIdRaw = await retry(async () => {
          const t = await bdio.bdioToTokenId(bdioId);
          if (t.toString() === '0') throw new Error('Token ID not yet available');
          return t;
        }, 5, 2000); // Retry up to 5 times with 2 second delay

        finalTokenId = Number(tokenIdRaw);
      } catch (e) {
        return res.status(400).json({
          error: 'NFT not minted yet or token ID not available',
          details: e.message
        });
      }
    }

    // Get document info for enhanced metadata
    let documentInfo = null;
    try {
      const [owner, active, archived, timestamp, versionCount, category] = await bdio.verifyDocument(bdioId);
      documentInfo = {
        owner,
        active,
        archived,
        timestamp: Number(timestamp),
        versionCount: Number(versionCount),
        category
      };
    } catch (e) {
      console.warn(`Could not get document info for ${bdioId}:`, e.message);
    }

    // Create comprehensive NFT metadata
    const nftData = {
      // Basic NFT metadata
      bdioId,
      tokenId: Number(finalTokenId),
      contract: contract || CONTRACT_CONFIG.address,
      name: name || `BDIO Document #${bdioId}`,
      description: description || 'Blockchain Document Identity Object (BDIO) NFT - Immutable proof of document authenticity and ownership.',
      image: image || `${CONTRACT_CONFIG.baseUrl}/assets/nft.png`,
      external_url: external_url || `${CONTRACT_CONFIG.baseUrl}/get?bdioId=${bdioId}`,

      // Enhanced attributes
      attributes: [
        // Default attributes
        { trait_type: 'BDIO ID', value: bdioId },
        { trait_type: 'Token ID', value: Number(finalTokenId) },
        { trait_type: 'Contract Address', value: contract || CONTRACT_CONFIG.address },
        { trait_type: 'Network', value: CONTRACT_CONFIG.network },
        { trait_type: 'Minted At', value: new Date().toISOString() },

        // Document-specific attributes (if available)
        ...(documentInfo ? [
          { trait_type: 'Category', value: documentInfo.category || 'Document' },
          { trait_type: 'Document Created', value: new Date(documentInfo.timestamp * 1000).toISOString() },
          { trait_type: 'Version Count', value: documentInfo.versionCount },
          { trait_type: 'Status', value: documentInfo.active ? (documentInfo.archived ? 'Archived' : 'Active') : 'Inactive' },
          { trait_type: 'Owner', value: documentInfo.owner }
        ] : []),

        // Custom attributes from request
        ...(Array.isArray(attributes) ? attributes.filter(attr =>
          attr.trait_type && !['BDIO ID', 'Token ID', 'Contract Address', 'Network', 'Minted At'].includes(attr.trait_type)
        ) : [])
      ],

      // Additional metadata
      animation_url: null, // Could be used for interactive content
      background_color: null,
      youtube_url: null,

      // BDIO-specific metadata
      bdio_metadata: {
        verification_url: `${CONTRACT_CONFIG.baseUrl}/verify?bdioId=${bdioId}`,
        metadata_url: `${CONTRACT_CONFIG.baseUrl}/metadata/${bdioId}.meta.json`,
        contract_info: {
          address: contract || CONTRACT_CONFIG.address,
          network: CONTRACT_CONFIG.network,
          standard: 'ERC721'
        }
      }
    };

    // Save NFT metadata file
    const filePath = path.join(metadataDir, `${finalTokenId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(nftData, null, 2), 'utf-8');

    // Verify file was written
    if (!fs.existsSync(filePath)) {
      throw new Error('File write failed - file does not exist after write');
    }

    console.log(`✅ Saved NFT metadata for tokenId ${finalTokenId} (BDIO: ${bdioId})`);

    res.json({
      message: 'NFT metadata saved successfully',
      tokenId: Number(finalTokenId),
      bdioId,
      filePath: `${finalTokenId}.json`,
      metadataUrl: `${CONTRACT_CONFIG.baseUrl}/metadata/${finalTokenId}.json`,
      openSeaUrl: `https://opensea.io/assets/${CONTRACT_CONFIG.network === 'polygon' ? 'matic' : 'ethereum'}/${contract || CONTRACT_CONFIG.address}/${finalTokenId}`
    });

  } catch (e) {
    console.error('❌ Error saving NFT metadata:', e);
    res.status(500).json({
      error: e.message || 'Failed to save NFT metadata',
      details: e.stack
    });
  }
});

// ========== Additional API endpoints ==========

// Get document metadata
app.get('/api/metadata/:bdioId', (req, res) => {
  try {
    const { bdioId } = req.params;
    const filePath = path.join(metadataDir, `${bdioId}.meta.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Metadata not found' });
    }

    const metadata = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    res.json(metadata);
  } catch (e) {
    console.error('❌ Error getting metadata:', e);
    res.status(500).json({ error: 'Failed to get metadata' });
  }
});

// Get NFT metadata
app.get('/api/nft/:tokenId', (req, res) => {
  try {
    const { tokenId } = req.params;
    const filePath = path.join(metadataDir, `${tokenId}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'NFT metadata not found' });
    }

    const nftData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    res.json(nftData);
  } catch (e) {
    console.error('❌ Error getting NFT metadata:', e);
    res.status(500).json({ error: 'Failed to get NFT metadata' });
  }
});

// Health check endpoint
app.get('/api/health', async (_, res) => {
  try {
    // Check contract connection
    const contractOwner = await bdio.contractOwner();

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      contract: {
        address: CONTRACT_CONFIG.address,
        network: CONTRACT_CONFIG.network,
        owner: contractOwner,
        connected: true
      },
      server: {
        port: PORT,
        baseUrl: CONTRACT_CONFIG.baseUrl
      }
    });
  } catch (e) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: e.message,
      contract: {
        address: CONTRACT_CONFIG.address,
        network: CONTRACT_CONFIG.network,
        connected: false
      }
    });
  }
});

// ========== updateMetadataForAll (Background Task) ==========
const updateMetadataForAll = async () => {
  try {
    console.log('🔄 Starting background metadata update...');
    const files = fs.readdirSync(metadataDir).filter(f => f.endsWith('.meta.json'));

    if (files.length === 0) {
      console.log('ℹ️ No metadata files found to update');
      return;
    }

    console.log(`📊 Found ${files.length} metadata files to update`);

    for (const file of files) {
      const bdioId = path.basename(file, '.meta.json');
      try {
        // Use internal function instead of HTTP call for better performance
        const exists = await bdio.exists(bdioId);
        if (exists) {
          console.log(`🔄 Updating ${bdioId}...`);
          // Simulate the updateMetadata API call internally
          const response = await fetch(`${CONTRACT_CONFIG.baseUrl}/api/updateMetadata`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ bdioId })
          });

          if (response.ok) {
            console.log(`✅ Updated ${bdioId}`);
          } else {
            console.warn(`⚠️ Failed to update ${bdioId}: ${response.statusText}`);
          }
        } else {
          console.log(`⚠️ Document ${bdioId} no longer exists in contract`);
        }
      } catch (e) {
        console.error(`❌ Error updating ${bdioId}:`, e.message);
      }

      // Small delay to avoid overwhelming the contract
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('✅ Background metadata update completed');
  } catch (e) {
    console.error('❌ Error in background metadata update:', e);
  }
};

// Run background update every 5 minutes (instead of 1 minute for better performance)
const BACKGROUND_UPDATE_INTERVAL = 5 * 60 * 1000; // 5 minutes
setInterval(() => updateMetadataForAll().catch(console.error), BACKGROUND_UPDATE_INTERVAL);

// Run initial update after 30 seconds
setTimeout(() => updateMetadataForAll().catch(console.error), 30000);

// ========== Error Handling ==========
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// ========== Start Server ==========
app.listen(PORT, () => {
  console.log(`🚀 Server running at ${CONTRACT_CONFIG.baseUrl}`);
  console.log(`📄 Contract: ${CONTRACT_CONFIG.address}`);
  console.log(`🌐 Network: ${CONTRACT_CONFIG.network}`);
  console.log(`📁 Metadata directory: ${metadataDir}`);
  console.log(`🔄 Background updates every ${BACKGROUND_UPDATE_INTERVAL / 1000 / 60} minutes`);
});
