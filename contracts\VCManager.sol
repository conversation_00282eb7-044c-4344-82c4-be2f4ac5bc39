// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract VCManager is ReentrancyGuard, Pausable {
    IBDIORegistry public bdioRegistry;
    address public contractOwner;
    address public pendingOwner;
    
    mapping(string => string) public vcHashes;

    uint256 public constant MAX_HASH_LENGTH = 128;
    uint256 public constant MAX_BATCH_SIZE = 50;

    event VCHashSet(string indexed bdioId, string vcHash);
    event RegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event OwnershipTransferInitiated(address indexed currentOwner, address indexed newOwner);
    event OwnershipTransferCompleted(address indexed oldOwner, address indexed newOwner);

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocumentOwner(string calldata bdioId) {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can set VC hash");
        _;
    }

    modifier validBdioId(string calldata bdioId) {
        require(bytes(bdioId).length >= 10 && bytes(bdioId).length <= 64, "Invalid BDIO ID");
        _;
    }

    modifier validVCHash(string calldata vcHash) {
        require(bytes(vcHash).length <= MAX_HASH_LENGTH, "VC hash too long");
        _;
    }

    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }

    modifier validContract(address addr) {
        require(addr != address(0), "Invalid address");
        require(addr.code.length > 0, "Not a contract");
        _;
    }

    constructor(address registryAddress) {
        require(registryAddress != address(0), "Invalid registry address");
        bdioRegistry = IBDIORegistry(registryAddress);
        contractOwner = msg.sender;
    }

    function setVCHash(string calldata bdioId, string calldata newVCHash) external 
        onlyDocumentOwner(bdioId)
        validBdioId(bdioId)
        validVCHash(newVCHash)
        whenNotPaused
        nonReentrant
    {
        vcHashes[bdioId] = newVCHash;
        emit VCHashSet(bdioId, newVCHash);
    }

    function batchSetVCHash(
        string[] calldata bdioIds, 
        string[] calldata newVCHashes
    ) external 
        whenNotPaused
        nonReentrant
    {
        require(bdioIds.length == newVCHashes.length, "Mismatched arrays");
        require(bdioIds.length > 0 && bdioIds.length <= MAX_BATCH_SIZE, "Invalid batch size");
        
        for (uint i = 0; i < bdioIds.length; i++) {
            require(bytes(bdioIds[i]).length >= 10 && bytes(bdioIds[i]).length <= 64, "Invalid BDIO ID");
            require(bytes(newVCHashes[i]).length <= MAX_HASH_LENGTH, "VC hash too long");
            require(msg.sender == bdioRegistry.getDocumentOwner(bdioIds[i]), "Not document owner");
            
            vcHashes[bdioIds[i]] = newVCHashes[i];
            emit VCHashSet(bdioIds[i], newVCHashes[i]);
        }
    }

    function vcHash(string calldata bdioId) external view returns (string memory) {
        return vcHashes[bdioId];
    }

    /// @notice Update registry address (only owner)
    function updateRegistry(address newRegistry) external
        onlyContractOwner
        validContract(newRegistry)
    {
        address oldRegistry = address(bdioRegistry);
        bdioRegistry = IBDIORegistry(newRegistry);
        emit RegistryUpdated(oldRegistry, newRegistry);
    }

    /// Emergency pause
    function pause() external onlyContractOwner {
        _pause();
    }

    function unpause() external onlyContractOwner {
        _unpause();
    }

    /// @notice Initiate ownership transfer (2-step process)
    function transferOwnership(address newOwner) external onlyContractOwner validAddress(newOwner) {
        require(newOwner != contractOwner, "Same owner");
        pendingOwner = newOwner;
        emit OwnershipTransferInitiated(contractOwner, newOwner);
    }

    /// @notice Accept ownership transfer
    function acceptOwnership() external {
        require(msg.sender == pendingOwner, "Not pending owner");
        address oldOwner = contractOwner;
        contractOwner = pendingOwner;
        pendingOwner = address(0);
        emit OwnershipTransferCompleted(oldOwner, contractOwner);
    }

    /// @notice Cancel pending ownership transfer
    function cancelOwnershipTransfer() external onlyContractOwner {
        require(pendingOwner != address(0), "No pending transfer");
        pendingOwner = address(0);
    }
}