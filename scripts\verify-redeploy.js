const hre = require("hardhat");

async function main() {
  // ganti ini ke address kontrak baru yang kamu deploy
  const contractAddress = "******************************************";

  // constructor argument: address AccessControlManager
  const accessControlManager = "******************************************";

  console.log("Verifying contract on Etherscan...");
  await hre.run("verify:verify", {
    address: contractAddress,
    constructorArguments: [accessControlManager],
  });

  console.log("✅ Verification done!");
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
