const hre = require("hardhat");

async function main() {
  const AccessControlManagerAddress = "******************************************"; // Confirmed from deployments.json
  const BDIOCoreRegistryAddress = "******************************************"; // Confirmed from deployments.json

  const BDIOExpiryManager = await hre.ethers.getContractFactory("BDIOExpiryManager");
  const expiryManager = await BDIOExpiryManager.deploy(AccessControlManagerAddress, BDIOCoreRegistryAddress);
  await expiryManager.deploymentTransaction();

  console.log("BDIOExpiryManager deployed to:", expiryManager.target);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
